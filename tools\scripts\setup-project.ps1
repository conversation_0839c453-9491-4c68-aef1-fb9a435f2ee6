# PowerShell script to set up a new full-stack project
param(
    [Parameter(Mandatory=$true)]
    [string]$ProjectName,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("react-ts", "next-ts", "node-ts", "python-fastapi", "microservices")]
    [string]$Template = "react-ts",
    
    [Parameter(Mandatory=$false)]
    [switch]$WithDocker,
    
    [Parameter(Mandatory=$false)]
    [switch]$WithTesting,
    
    [Parameter(Mandatory=$false)]
    [switch]$WithCI
)

Write-Host "🚀 Setting up project: $ProjectName with template: $Template" -ForegroundColor Green

# Create project directory
$ProjectPath = Join-Path (Get-Location) $ProjectName
New-Item -ItemType Directory -Path $ProjectPath -Force | Out-Null
Set-Location $ProjectPath

# Initialize git repository
git init
Write-Host "✅ Git repository initialized" -ForegroundColor Green

# Create basic project structure
$Directories = @(
    "src",
    "tests",
    "docs",
    "scripts"
)

if ($WithDocker) {
    $Directories += "docker"
}

foreach ($Dir in $Directories) {
    New-Item -ItemType Directory -Path $Dir -Force | Out-Null
}

Write-Host "✅ Project structure created" -ForegroundColor Green

# Set up based on template
switch ($Template) {
    "react-ts" {
        Write-Host "🔧 Setting up React TypeScript project..." -ForegroundColor Yellow
        
        # Create package.json
        $PackageJson = @{
            name = $ProjectName.ToLower()
            version = "1.0.0"
            private = $true
            scripts = @{
                dev = "vite"
                build = "tsc && vite build"
                preview = "vite preview"
                test = "jest"
                "test:watch" = "jest --watch"
                "test:coverage" = "jest --coverage"
                lint = "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
                "lint:fix" = "eslint src --ext ts,tsx --fix"
                format = "prettier --write src/**/*.{ts,tsx,css,md}"
            }
            dependencies = @{
                react = "^18.2.0"
                "react-dom" = "^18.2.0"
                antd = "^5.12.0"
                "@ant-design/icons" = "^5.2.0"
                "react-router-dom" = "^6.20.0"
                "@reduxjs/toolkit" = "^1.9.0"
                "react-redux" = "^8.1.0"
                "@tanstack/react-query" = "^5.8.0"
                axios = "^1.6.0"
            }
            devDependencies = @{
                "@types/react" = "^18.2.0"
                "@types/react-dom" = "^18.2.0"
                "@typescript-eslint/eslint-plugin" = "^6.0.0"
                "@typescript-eslint/parser" = "^6.0.0"
                "@vitejs/plugin-react" = "^4.0.0"
                eslint = "^8.45.0"
                "eslint-plugin-react-hooks" = "^4.6.0"
                "eslint-plugin-react-refresh" = "^0.4.0"
                jest = "^29.7.0"
                "@testing-library/react" = "^13.4.0"
                "@testing-library/jest-dom" = "^6.1.0"
                "@testing-library/user-event" = "^14.5.0"
                prettier = "^3.0.0"
                typescript = "^5.0.0"
                vite = "^4.4.0"
            }
        }
        
        $PackageJson | ConvertTo-Json -Depth 10 | Out-File -FilePath "package.json" -Encoding UTF8
        
        # Create TypeScript config
        $TsConfig = @{
            compilerOptions = @{
                target = "ES2020"
                lib = @("ES2020", "DOM", "DOM.Iterable")
                module = "ESNext"
                skipLibCheck = $true
                moduleResolution = "bundler"
                allowImportingTsExtensions = $true
                resolveJsonModule = $true
                isolatedModules = $true
                noEmit = $true
                jsx = "react-jsx"
                strict = $true
                noUnusedLocals = $true
                noUnusedParameters = $true
                noFallthroughCasesInSwitch = $true
                baseUrl = "."
                paths = @{
                    "@/*" = @("./src/*")
                }
            }
            include = @("src")
            references = @(@{ path = "./tsconfig.node.json" })
        }
        
        $TsConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath "tsconfig.json" -Encoding UTF8
        
        # Create Vite config
        $ViteConfig = @"
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})
"@
        $ViteConfig | Out-File -FilePath "vite.config.ts" -Encoding UTF8
        
        # Create basic React app structure
        $AppTsx = @"
import React from 'react'
import { ConfigProvider } from 'antd'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Provider } from 'react-redux'
import { store } from './store'
import HomePage from './pages/HomePage'
import './App.css'

const queryClient = new QueryClient()

function App() {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider>
          <Router>
            <div className="App">
              <Routes>
                <Route path="/" element={<HomePage />} />
              </Routes>
            </div>
          </Router>
        </ConfigProvider>
      </QueryClientProvider>
    </Provider>
  )
}

export default App
"@
        $AppTsx | Out-File -FilePath "src/App.tsx" -Encoding UTF8
        
        # Create main.tsx
        $MainTsx = @"
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
"@
        $MainTsx | Out-File -FilePath "src/main.tsx" -Encoding UTF8
        
        # Create Redux store
        New-Item -ItemType Directory -Path "src/store" -Force | Out-Null
        $StoreTsx = @"
import { configureStore } from '@reduxjs/toolkit'

export const store = configureStore({
  reducer: {
    // Add your reducers here
  },
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
"@
        $StoreTsx | Out-File -FilePath "src/store/index.ts" -Encoding UTF8
        
        # Create HomePage component
        New-Item -ItemType Directory -Path "src/pages" -Force | Out-Null
        $HomePageTsx = @"
import React from 'react'
import { Typography, Button, Space } from 'antd'
import { RocketOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

const HomePage: React.FC = () => {
  return (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <Space direction="vertical" size="large">
        <RocketOutlined style={{ fontSize: '64px', color: '#1890ff' }} />
        <Title level={1}>Welcome to $ProjectName</Title>
        <Paragraph>
          Your React TypeScript project is ready to go!
        </Paragraph>
        <Button type="primary" size="large">
          Get Started
        </Button>
      </Space>
    </div>
  )
}

export default HomePage
"@
        $HomePageTsx | Out-File -FilePath "src/pages/HomePage.tsx" -Encoding UTF8
        
        # Create CSS files
        $AppCss = @"
.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
"@
        $AppCss | Out-File -FilePath "src/App.css" -Encoding UTF8
        
        $IndexCss = @"
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}
"@
        $IndexCss | Out-File -FilePath "src/index.css" -Encoding UTF8
        
        # Create index.html
        $IndexHtml = @"
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>$ProjectName</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
"@
        $IndexHtml | Out-File -FilePath "index.html" -Encoding UTF8
    }
    
    "python-fastapi" {
        Write-Host "🔧 Setting up Python FastAPI project..." -ForegroundColor Yellow
        
        # Create pyproject.toml
        $PyProjectToml = @"
[tool.poetry]
name = "$($ProjectName.ToLower())"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
sqlalchemy = "^2.0.0"
alembic = "^1.13.0"
psycopg2-binary = "^2.9.0"
redis = "^5.0.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.0"}
python-multipart = "^0.0.6"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
black = "^23.11.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"
pre-commit = "^3.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
"@
        $PyProjectToml | Out-File -FilePath "pyproject.toml" -Encoding UTF8
        
        # Create main FastAPI app
        $MainPy = @"
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="$ProjectName API",
    description="A FastAPI application",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Welcome to $ProjectName API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
"@
        $MainPy | Out-File -FilePath "src/main.py" -Encoding UTF8
    }
}

# Create Docker configuration if requested
if ($WithDocker) {
    Write-Host "🐳 Setting up Docker configuration..." -ForegroundColor Yellow
    
    if ($Template -eq "react-ts") {
        $Dockerfile = @"
# Multi-stage build for React app
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"@
        $Dockerfile | Out-File -FilePath "Dockerfile" -Encoding UTF8
        
        # Create nginx config
        $NginxConf = @"
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files `$uri `$uri/ /index.html;
        }
        
        location /api {
            proxy_pass http://backend:8000;
            proxy_set_header Host `$host;
            proxy_set_header X-Real-IP `$remote_addr;
        }
    }
}
"@
        $NginxConf | Out-File -FilePath "docker/nginx.conf" -Encoding UTF8
    }
    
    # Create docker-compose.yml
    $DockerCompose = @"
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ${ProjectName.ToLower()}_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
"@
    $DockerCompose | Out-File -FilePath "docker-compose.yml" -Encoding UTF8
}

# Create testing configuration if requested
if ($WithTesting) {
    Write-Host "🧪 Setting up testing configuration..." -ForegroundColor Yellow
    
    if ($Template -eq "react-ts") {
        # Create Jest config
        $JestConfig = @"
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
"@
        $JestConfig | Out-File -FilePath "jest.config.js" -Encoding UTF8
        
        # Create setupTests.ts
        $SetupTests = @"
import '@testing-library/jest-dom'
"@
        $SetupTests | Out-File -FilePath "src/setupTests.ts" -Encoding UTF8
    }
}

# Create CI/CD configuration if requested
if ($WithCI) {
    Write-Host "⚙️ Setting up CI/CD configuration..." -ForegroundColor Yellow
    
    New-Item -ItemType Directory -Path ".github/workflows" -Force | Out-Null
    
    $CIWorkflow = @"
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
    
    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: echo "Deploy to production"
"@
    $CIWorkflow | Out-File -FilePath ".github/workflows/ci.yml" -Encoding UTF8
}

# Create README.md
$ReadmeMd = @"
# $ProjectName

## Description
A modern full-stack application built with best practices and senior-level patterns.

## Technology Stack
- **Template**: $Template
- **Docker**: $(if ($WithDocker) { "✅ Enabled" } else { "❌ Disabled" })
- **Testing**: $(if ($WithTesting) { "✅ Enabled" } else { "❌ Disabled" })
- **CI/CD**: $(if ($WithCI) { "✅ Enabled" } else { "❌ Disabled" })

## Getting Started

### Prerequisites
- Node.js 18+
- Docker and Docker Compose (if using Docker)

### Installation
``````bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
``````

### Docker Development
``````bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
``````

## Project Structure
``````
$ProjectName/
├── src/                 # Source code
├── tests/              # Test files
├── docs/               # Documentation
├── scripts/            # Build and deployment scripts
$(if ($WithDocker) { "├── docker/             # Docker configurations" })
└── README.md           # This file
``````

## Development Workflow
1. Create feature branch from `develop`
2. Make changes and add tests
3. Run linting and tests locally
4. Create pull request to `develop`
5. After review, merge to `develop`
6. Deploy to staging for testing
7. Merge to `main` for production deployment

## Contributing
Please read the contributing guidelines before making changes.

## License
This project is licensed under the MIT License.
"@
$ReadmeMd | Out-File -FilePath "README.md" -Encoding UTF8

# Create .gitignore
$GitIgnore = @"
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
/dist
/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Coverage
coverage/
*.lcov

# Cache
.cache/
.parcel-cache/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Database
*.db
*.sqlite3

# Docker
.dockerignore
"@
$GitIgnore | Out-File -FilePath ".gitignore" -Encoding UTF8

Write-Host "🎉 Project $ProjectName has been successfully created!" -ForegroundColor Green
Write-Host "📁 Location: $ProjectPath" -ForegroundColor Cyan
Write-Host "🚀 Next steps:" -ForegroundColor Yellow
Write-Host "   1. cd $ProjectName" -ForegroundColor White
Write-Host "   2. npm install" -ForegroundColor White
Write-Host "   3. npm run dev" -ForegroundColor White

if ($WithDocker) {
    Write-Host "   4. docker-compose up -d (for full stack)" -ForegroundColor White
}
