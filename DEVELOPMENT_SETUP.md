# Development Environment Setup

This guide helps you set up a comprehensive development environment for senior-level full-stack development.

## Prerequisites

### Required Software
- **Node.js** (v18+ LTS) - JavaScript runtime
- **Python** (v3.11+) - Backend development
- **Docker** & **Docker Compose** - Containerization
- **PostgreSQL** (v15+) - Primary database
- **Redis** (v7+) - Caching and message broker
- **Git** - Version control
- **VS Code** or **JetBrains IDEs** - Code editor

### Cloud CLI Tools
- **AWS CLI** - Amazon Web Services
- **gcloud CLI** - Google Cloud Platform
- **kubectl** - Kubernetes cluster management
- **terraform** - Infrastructure as Code

## Environment Configuration

### 1. Node.js Development

```bash
# Install Node.js version manager (nvm)
# Windows: Use nvm-windows
# macOS/Linux: Use nvm

# Install and use Node.js LTS
nvm install --lts
nvm use --lts

# Global packages for development
npm install -g @nestjs/cli create-react-app typescript ts-node nodemon
npm install -g @storybook/cli webpack-cli vite
npm install -g eslint prettier jest
```

### 2. Python Development

```bash
# Install Python package manager
pip install pipenv poetry

# Global development tools
pip install black flake8 mypy pytest pytest-cov
pip install fastapi uvicorn django djangorestframework
pip install sqlalchemy alembic psycopg2-binary redis
```

### 3. Database Setup

#### PostgreSQL Configuration
```sql
-- Create development databases
CREATE DATABASE ecommerce_dev;
CREATE DATABASE collaboration_dev;
CREATE DATABASE analytics_dev;

-- Create test databases
CREATE DATABASE ecommerce_test;
CREATE DATABASE collaboration_test;
CREATE DATABASE analytics_test;

-- Create development user
CREATE USER dev_user WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE ecommerce_dev TO dev_user;
GRANT ALL PRIVILEGES ON DATABASE collaboration_dev TO dev_user;
GRANT ALL PRIVILEGES ON DATABASE analytics_dev TO dev_user;
```

#### Redis Configuration
```bash
# Redis configuration for development
# Enable persistence and configure memory limits
redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
```

### 4. Docker Development Environment

Create a `docker-compose.dev.yml` for local development:

```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: dev_db
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
```

## IDE Configuration

### VS Code Extensions
- **TypeScript/JavaScript**: TypeScript Hero, ES7+ React/Redux/React-Native snippets
- **Python**: Python, Pylance, Python Docstring Generator
- **Database**: PostgreSQL, Redis
- **DevOps**: Docker, Kubernetes, Terraform
- **Testing**: Jest, Test Explorer UI
- **Code Quality**: ESLint, Prettier, SonarLint
- **Git**: GitLens, Git Graph

### VS Code Settings
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.testing.pytestEnabled": true
}
```

## Environment Variables

Create a `.env.template` file for each project:

```bash
# Database Configuration
DATABASE_URL=postgresql://dev_user:dev_password@localhost:5432/dev_db
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=localhost
API_PORT=8000
JWT_SECRET=your-jwt-secret-key

# Cloud Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Monitoring
LOG_LEVEL=debug
SENTRY_DSN=your-sentry-dsn
```

## Development Workflow

### 1. Project Initialization
```bash
# Create new project structure
mkdir project-name && cd project-name
git init
npm init -y  # or poetry init for Python

# Set up development environment
cp ../.env.template .env
docker-compose -f docker-compose.dev.yml up -d
```

### 2. Code Quality Setup
```bash
# TypeScript/React project
npx eslint --init
npx prettier --init
npm install --save-dev husky lint-staged

# Python project
poetry add --group dev black flake8 mypy pytest
poetry add --group dev pre-commit
pre-commit install
```

### 3. Testing Setup
```bash
# Frontend testing
npm install --save-dev jest @testing-library/react @testing-library/jest-dom
npm install --save-dev cypress @playwright/test

# Backend testing
poetry add --group dev pytest pytest-asyncio pytest-cov
poetry add --group dev factory-boy faker
```

## Performance Monitoring

### Application Performance Monitoring (APM)
- **Frontend**: Web Vitals, Lighthouse CI
- **Backend**: New Relic, DataDog, or Sentry Performance
- **Database**: pg_stat_statements, Redis monitoring
- **Infrastructure**: Prometheus + Grafana

### Local Development Monitoring
```bash
# Install monitoring tools
npm install --save-dev webpack-bundle-analyzer
npm install --save-dev @storybook/addon-performance
pip install django-debug-toolbar  # for Django projects
```

## Security Configuration

### Development Security Checklist
- [ ] Environment variables properly configured
- [ ] Database connections use SSL in production
- [ ] API endpoints have proper authentication
- [ ] CORS configured correctly
- [ ] Input validation implemented
- [ ] SQL injection prevention measures
- [ ] XSS protection enabled
- [ ] CSRF tokens implemented

---

*This setup provides a robust foundation for senior-level full-stack development. Adjust configurations based on specific project requirements.*
