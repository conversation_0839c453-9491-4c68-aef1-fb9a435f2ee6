# Healthcare Marketplace Implementation

## 🏥 Complete Implementation Guide for Your AI Healthcare Platform

This section provides a comprehensive, step-by-step implementation guide specifically tailored for your healthcare AI platform where doctors set availability and receive payments for consultations with a 2% platform commission.

---

## 📁 Section Contents

### **Implementation Guides**
- [`use-case-analysis.md`](./use-case-analysis.md) - Detailed analysis of your specific requirements
- [`step-by-step-setup.md`](./step-by-step-setup.md) - Complete implementation walkthrough
- [`doctor-onboarding.md`](./doctor-onboarding.md) - Doctor account creation and verification
- [`payment-processing.md`](./payment-processing.md) - Patient payment and commission handling

### **Advanced Features**
- [`appointment-management.md`](./appointment-management.md) - Booking, scheduling, and availability
- [`payout-management.md`](./payout-management.md) - Doctor earnings and payout automation
- [`compliance-implementation.md`](./compliance-implementation.md) - HIPAA and healthcare regulations
- [`international-doctors.md`](./international-doctors.md) - Supporting global healthcare providers

### **Operations & Monitoring**
- [`dashboard-analytics.md`](./dashboard-analytics.md) - Platform analytics and reporting
- [`customer-support.md`](./customer-support.md) - Support tools and dispute resolution
- [`scaling-considerations.md`](./scaling-considerations.md) - Growth and performance optimization

---

## 🎯 Your Platform Overview

### **Business Model Analysis**

**Platform Type**: Healthcare AI Marketplace
**Primary Users**: Doctors (sellers) and Patients (buyers)
**Revenue Model**: 2% commission on consultations
**Payment Flow**: Patient → Platform → Doctor (minus commission)

### **Key Requirements**

```
Core Features:
├── Doctor Registration & Verification
├── Availability Management
├── Patient Booking & Payment
├── Automated Commission Handling
├── Doctor Payout Management
├── Compliance & Security
└── Analytics & Reporting

Technical Requirements:
├── Stripe Connect Integration
├── Express Account Management
├── Payment Intent Processing
├── Webhook Event Handling
├── HIPAA Compliance
├── Multi-currency Support
└── Mobile Optimization
```

### **Implementation Architecture**

```
┌─────────────────────────────────────────────────────────────────┐
│                    Healthcare AI Platform                      │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Patient App   │  Platform Core  │    Doctor Portal            │
│                 │                 │                             │
│ • Booking UI    │ • User Mgmt     │ • Profile Management        │
│ • Payment Form  │ • Scheduling    │ • Availability Setting      │
│ • Consultation  │ • Commission    │ • Earnings Dashboard        │
│   History       │   Handling      │ • Payout Management         │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                 │                         │
         ▼                 ▼                         ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ Stripe Payment  │ │ Stripe Connect  │ │ Doctor Stripe   │
│ Processing      │ │ Platform        │ │ Accounts        │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

---

## 🚀 Quick Start Implementation

### **Phase 1: Foundation Setup (Week 1)**

**1. Stripe Account Setup**
```javascript
// Set up Stripe Connect platform
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Enable Connect in Stripe Dashboard
// Get your Connect client ID: ca_xxxxx
const STRIPE_CLIENT_ID = process.env.STRIPE_CLIENT_ID;
```

**2. Basic Express Account Creation**
```javascript
// Create doctor Express account
async function createDoctorAccount(doctorData) {
  const account = await stripe.accounts.create({
    type: 'express',
    country: doctorData.country || 'US',
    email: doctorData.email,
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true }
    },
    business_type: 'individual',
    individual: {
      first_name: doctorData.firstName,
      last_name: doctorData.lastName,
      email: doctorData.email
    },
    metadata: {
      doctor_id: doctorData.id,
      specialty: doctorData.specialty,
      platform: 'healthcare_ai'
    }
  });
  
  return account;
}
```

**3. Basic Payment Processing**
```javascript
// Process consultation payment
async function processConsultationPayment(appointmentData) {
  const { doctorId, consultationFee, appointmentId } = appointmentData;
  
  const doctor = await getDoctorById(doctorId);
  const amount = Math.round(consultationFee * 100); // Convert to cents
  const platformFee = Math.round(amount * 0.02); // 2% commission
  
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount,
    currency: 'usd',
    application_fee_amount: platformFee,
    transfer_data: {
      destination: doctor.stripe_account_id
    },
    metadata: {
      appointment_id: appointmentId,
      doctor_id: doctorId
    }
  });
  
  return paymentIntent;
}
```

### **Phase 2: Core Features (Week 2-3)**

**1. Doctor Onboarding Flow**
```javascript
// Complete doctor onboarding
async function onboardDoctor(doctorData) {
  // 1. Create Stripe account
  const account = await createDoctorAccount(doctorData);
  
  // 2. Save to database
  await saveDoctorStripeAccount(doctorData.id, account.id);
  
  // 3. Generate onboarding link
  const accountLink = await stripe.accountLinks.create({
    account: account.id,
    refresh_url: `${BASE_URL}/doctor/stripe-refresh`,
    return_url: `${BASE_URL}/doctor/stripe-success`,
    type: 'account_onboarding'
  });
  
  return {
    account_id: account.id,
    onboarding_url: accountLink.url
  };
}
```

**2. Webhook Event Handling**
```javascript
// Handle payment success
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  switch (event.type) {
    case 'payment_intent.succeeded':
      handleSuccessfulPayment(event.data.object);
      break;
    case 'account.updated':
      handleAccountUpdate(event.data.object);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({received: true});
});
```

### **Phase 3: Advanced Features (Week 4-6)**

**1. Appointment Management**
```javascript
// Complete appointment lifecycle
class AppointmentManager {
  async createAppointment(appointmentData) {
    // 1. Validate availability
    const available = await this.checkAvailability(
      appointmentData.doctorId, 
      appointmentData.time
    );
    
    if (!available) {
      throw new Error('Time slot not available');
    }
    
    // 2. Create payment intent
    const paymentIntent = await processConsultationPayment(appointmentData);
    
    // 3. Reserve time slot
    await this.reserveTimeSlot(
      appointmentData.doctorId, 
      appointmentData.time, 
      paymentIntent.id
    );
    
    return {
      appointment_id: appointmentData.id,
      client_secret: paymentIntent.client_secret
    };
  }
  
  async confirmAppointment(paymentIntentId) {
    // Called from webhook after successful payment
    const appointment = await this.getAppointmentByPaymentIntent(paymentIntentId);
    
    // 1. Confirm appointment
    await this.updateAppointmentStatus(appointment.id, 'confirmed');
    
    // 2. Send notifications
    await this.notifyPatient(appointment);
    await this.notifyDoctor(appointment);
    
    // 3. Update doctor availability
    await this.markTimeSlotBooked(appointment.doctor_id, appointment.time);
    
    return appointment;
  }
}
```

**2. Doctor Dashboard**
```javascript
// Doctor earnings and payout management
async function getDoctorDashboard(doctorId) {
  const doctor = await getDoctorById(doctorId);
  
  // Get earnings data
  const earnings = await getDoctorEarnings(doctorId);
  
  // Get upcoming appointments
  const appointments = await getUpcomingAppointments(doctorId);
  
  // Get account status
  const accountStatus = await checkAccountStatus(doctor.stripe_account_id);
  
  return {
    doctor: doctor,
    earnings: earnings,
    appointments: appointments,
    account_status: accountStatus,
    payout_schedule: accountStatus.payout_schedule
  };
}
```

---

## 💰 Commission & Payout Management

### **Commission Calculation**

```javascript
// Flexible commission calculation
class CommissionCalculator {
  calculateCommission(consultationFee, doctorTier = 'standard') {
    const commissionRates = {
      'standard': 0.02,    // 2% for standard doctors
      'premium': 0.015,    // 1.5% for premium doctors
      'partner': 0.01      // 1% for partner doctors
    };
    
    const rate = commissionRates[doctorTier] || 0.02;
    return Math.round(consultationFee * 100 * rate); // In cents
  }
  
  calculateDoctorEarnings(consultationFee, doctorTier = 'standard') {
    const totalAmount = consultationFee * 100; // In cents
    const commission = this.calculateCommission(consultationFee, doctorTier);
    const doctorEarnings = totalAmount - commission;
    
    return {
      total_amount: totalAmount,
      platform_commission: commission,
      doctor_earnings: doctorEarnings,
      commission_rate: commission / totalAmount
    };
  }
}
```

### **Automated Payout Management**

```javascript
// Manage doctor payouts
class PayoutManager {
  async setupDoctorPayouts(doctorId, schedule = 'weekly') {
    const doctor = await getDoctorById(doctorId);
    
    // Update payout schedule
    await stripe.accounts.update(doctor.stripe_account_id, {
      settings: {
        payouts: {
          schedule: {
            interval: schedule, // 'daily', 'weekly', 'monthly'
            weekly_anchor: 'friday' // For weekly payouts
          }
        }
      }
    });
    
    return { success: true, schedule: schedule };
  }
  
  async getPayoutHistory(doctorId, limit = 10) {
    const doctor = await getDoctorById(doctorId);
    
    const payouts = await stripe.payouts.list({
      stripeAccount: doctor.stripe_account_id,
      limit: limit
    });
    
    return payouts.data.map(payout => ({
      id: payout.id,
      amount: payout.amount / 100, // Convert to dollars
      arrival_date: payout.arrival_date,
      status: payout.status,
      description: payout.description
    }));
  }
}
```

---

## 🔐 Healthcare Compliance Integration

### **HIPAA-Compliant Payment Processing**

```javascript
// Ensure HIPAA compliance in payment metadata
class HIPAACompliantPayments {
  sanitizePaymentMetadata(appointmentData) {
    // Remove any PHI from payment metadata
    return {
      // Safe: Non-PHI identifiers
      appointment_id: appointmentData.id,
      doctor_id: appointmentData.doctor_id,
      patient_id: appointmentData.patient_id, // Internal ID, not SSN
      
      // Safe: General service information
      service_type: 'telemedicine_consultation',
      duration_minutes: appointmentData.duration,
      consultation_category: this.generalizeSpecialty(appointmentData.doctor_specialty),
      
      // Safe: Business information
      platform_fee_percentage: 2,
      
      // Avoid: Specific medical information
      // diagnosis: appointmentData.diagnosis, // ❌ PHI
      // symptoms: appointmentData.symptoms,   // ❌ PHI
      // treatment: appointmentData.treatment  // ❌ PHI
    };
  }
  
  generalizeSpecialty(specialty) {
    const generalCategories = {
      'cardiology': 'specialist_consultation',
      'dermatology': 'specialist_consultation',
      'psychiatry': 'mental_health_consultation',
      'general_practice': 'general_consultation'
    };
    
    return generalCategories[specialty] || 'medical_consultation';
  }
}
```

### **Medical License Verification**

```javascript
// Verify doctor credentials before account activation
class DoctorVerification {
  async verifyDoctor(doctorData) {
    const verificationResults = {
      identity_verified: false,
      license_verified: false,
      credentials_verified: false,
      ready_for_activation: false
    };
    
    // 1. Identity verification (handled by Stripe)
    const accountStatus = await stripe.accounts.retrieve(doctorData.stripe_account_id);
    verificationResults.identity_verified = accountStatus.individual?.verification?.status === 'verified';
    
    // 2. Medical license verification
    verificationResults.license_verified = await this.verifyMedicalLicense({
      license_number: doctorData.license_number,
      state: doctorData.license_state,
      last_name: doctorData.last_name
    });
    
    // 3. Professional credentials
    verificationResults.credentials_verified = await this.verifyCredentials({
      medical_school: doctorData.medical_school,
      graduation_year: doctorData.graduation_year,
      board_certifications: doctorData.board_certifications
    });
    
    // 4. Overall readiness
    verificationResults.ready_for_activation = 
      verificationResults.identity_verified &&
      verificationResults.license_verified &&
      verificationResults.credentials_verified;
    
    return verificationResults;
  }
}
```

---

## 📊 Analytics & Monitoring

### **Platform Analytics Dashboard**

```javascript
// Comprehensive platform analytics
class PlatformAnalytics {
  async getPlatformMetrics(timeframe = '30d') {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));
    
    const metrics = {
      // Revenue metrics
      total_revenue: await this.getTotalRevenue(startDate),
      platform_commission: await this.getPlatformCommission(startDate),
      doctor_earnings: await this.getDoctorEarnings(startDate),
      
      // Transaction metrics
      total_consultations: await this.getTotalConsultations(startDate),
      successful_payments: await this.getSuccessfulPayments(startDate),
      payment_success_rate: 0,
      
      // User metrics
      active_doctors: await this.getActiveDoctors(startDate),
      active_patients: await this.getActivePatients(startDate),
      new_doctor_signups: await this.getNewDoctorSignups(startDate),
      
      // Specialty breakdown
      specialty_distribution: await this.getSpecialtyDistribution(startDate),
      
      // Geographic metrics
      geographic_distribution: await this.getGeographicDistribution(startDate)
    };
    
    // Calculate derived metrics
    metrics.payment_success_rate = 
      metrics.successful_payments / metrics.total_consultations;
    
    metrics.average_consultation_fee = 
      metrics.total_revenue / metrics.total_consultations;
    
    return metrics;
  }
  
  async getDoctorPerformanceMetrics(doctorId, timeframe = '30d') {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));
    
    return {
      total_consultations: await this.getDoctorConsultations(doctorId, startDate),
      total_earnings: await this.getDoctorTotalEarnings(doctorId, startDate),
      average_rating: await this.getDoctorAverageRating(doctorId),
      patient_retention_rate: await this.getPatientRetentionRate(doctorId, startDate),
      availability_utilization: await this.getAvailabilityUtilization(doctorId, startDate)
    };
  }
}
```

---

## 🔧 Development Environment Setup

### **Local Development Setup**

```bash
# 1. Clone your project
git clone https://github.com/your-org/healthcare-platform.git
cd healthcare-platform

# 2. Install dependencies
npm install

# 3. Set up environment variables
cp .env.example .env
# Add your Stripe keys to .env:
# STRIPE_PUBLISHABLE_KEY=pk_test_...
# STRIPE_SECRET_KEY=sk_test_...
# STRIPE_WEBHOOK_SECRET=whsec_...
# STRIPE_CLIENT_ID=ca_...

# 4. Set up database
npm run db:migrate
npm run db:seed

# 5. Start development server
npm run dev
```

### **Stripe CLI Setup for Webhook Testing**

```bash
# Install Stripe CLI
# Windows: Download from https://github.com/stripe/stripe-cli/releases
# Mac: brew install stripe/stripe-cli/stripe
# Linux: Download binary

# Login to Stripe
stripe login

# Forward webhooks to local development
stripe listen --forward-to localhost:3000/webhook

# Test webhook events
stripe trigger payment_intent.succeeded
stripe trigger account.updated
```

---

## 📋 Implementation Checklist

### **Phase 1: Foundation (Week 1)**
- [ ] Set up Stripe Connect account
- [ ] Create basic Express account creation
- [ ] Implement basic payment processing
- [ ] Set up webhook endpoint
- [ ] Test with Stripe CLI

### **Phase 2: Core Features (Week 2-3)**
- [ ] Complete doctor onboarding flow
- [ ] Implement appointment booking
- [ ] Add payment confirmation handling
- [ ] Create basic doctor dashboard
- [ ] Add patient payment history

### **Phase 3: Advanced Features (Week 4-6)**
- [ ] Add medical license verification
- [ ] Implement HIPAA compliance measures
- [ ] Create analytics dashboard
- [ ] Add payout management
- [ ] Implement customer support tools

### **Phase 4: Production Readiness (Week 7-8)**
- [ ] Security audit and testing
- [ ] Performance optimization
- [ ] Compliance verification
- [ ] Production deployment
- [ ] Monitoring and alerting setup

---

## 🚀 Next Steps

### **Immediate Actions**
1. **Review Use Case Analysis**: Read [`use-case-analysis.md`](./use-case-analysis.md) for detailed requirements
2. **Follow Step-by-Step Guide**: Implement using [`step-by-step-setup.md`](./step-by-step-setup.md)
3. **Set Up Development Environment**: Use the setup guide above
4. **Start with Doctor Onboarding**: Begin with [`doctor-onboarding.md`](./doctor-onboarding.md)

### **Implementation Priority**
1. **Core Payment Flow**: Get basic payments working first
2. **Doctor Onboarding**: Enable doctor account creation
3. **Compliance**: Implement healthcare-specific requirements
4. **Analytics**: Add monitoring and reporting
5. **Optimization**: Performance and user experience improvements

---

**Ready to build your healthcare marketplace? Let's start with a detailed analysis of your use case!**

**Next**: [Use Case Analysis →](./use-case-analysis.md)
