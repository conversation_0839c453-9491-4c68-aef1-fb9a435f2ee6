# Code Examples

## 💻 Working Code Samples for Healthcare Marketplace

This section provides complete, working code examples for implementing your healthcare marketplace payment system using Stripe Connect.

---

## 📁 Code Examples Structure

### **Backend Examples (Node.js/Express)**
- [`marketplace-payments/`](./marketplace-payments/) - Complete marketplace payment implementation
- [`doctor-onboarding/`](./doctor-onboarding/) - Doctor account creation and verification
- [`webhook-handlers/`](./webhook-handlers/) - Event processing and business logic
- [`account-management/`](./account-management/) - Doctor account and payout management

### **Frontend Examples**
- [`payment-forms/`](./payment-forms/) - Secure payment form implementations
- [`doctor-dashboard/`](./doctor-dashboard/) - Doctor earnings and account management UI
- [`patient-booking/`](./patient-booking/) - Appointment booking and payment flow

### **Utilities & Helpers**
- [`compliance-helpers/`](./compliance-helpers/) - HIPAA and healthcare compliance utilities
- [`testing-examples/`](./testing-examples/) - Unit and integration tests
- [`deployment-configs/`](./deployment-configs/) - Production deployment configurations

---

## 🚀 Quick Start Example

### **Complete Healthcare Payment Flow**

Here's a minimal working example of the core payment flow:

**Backend (Node.js/Express)**:
```javascript
const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const app = express();

// Middleware
app.use(express.json());
app.use(express.raw({ type: 'application/json' }));

// Create payment intent for consultation
app.post('/api/create-payment-intent', async (req, res) => {
  try {
    const { doctor_id, patient_id, consultation_fee, appointment_id } = req.body;
    
    // Get doctor's Stripe account
    const doctor = await getDoctorById(doctor_id);
    if (!doctor.stripe_account_id) {
      return res.status(400).json({ error: 'Doctor payment setup incomplete' });
    }
    
    // Calculate fees
    const amount = Math.round(consultation_fee * 100); // Convert to cents
    const platformFee = Math.round(amount * 0.02); // 2% commission
    
    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: 'usd',
      application_fee_amount: platformFee,
      transfer_data: {
        destination: doctor.stripe_account_id
      },
      metadata: {
        appointment_id: appointment_id,
        doctor_id: doctor_id,
        patient_id: patient_id,
        consultation_fee: consultation_fee.toString()
      }
    });
    
    res.json({ client_secret: paymentIntent.client_secret });
    
  } catch (error) {
    console.error('Payment intent creation failed:', error);
    res.status(500).json({ error: 'Payment setup failed' });
  }
});

// Webhook handler
app.post('/webhook', (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      handleSuccessfulPayment(event.data.object);
      break;
    case 'account.updated':
      handleAccountUpdate(event.data.object);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});

async function handleSuccessfulPayment(paymentIntent) {
  const { appointment_id, doctor_id, patient_id } = paymentIntent.metadata;
  
  // Create confirmed appointment
  await createAppointment({
    id: appointment_id,
    doctor_id: doctor_id,
    patient_id: patient_id,
    status: 'confirmed',
    payment_status: 'paid',
    payment_intent_id: paymentIntent.id
  });
  
  // Send notifications
  await sendPatientConfirmation(appointment_id);
  await sendDoctorNotification(appointment_id);
}

app.listen(3000, () => console.log('Server running on port 3000'));
```

**Frontend (React)**:
```jsx
import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

const stripePromise = loadStripe('pk_test_...');

function PaymentForm({ appointmentData }) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    if (!stripe || !elements) return;

    // Create payment intent
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(appointmentData)
    });

    const { client_secret } = await response.json();

    // Confirm payment
    const { error } = await stripe.confirmCardPayment(client_secret, {
      payment_method: {
        card: elements.getElement(CardElement),
        billing_details: {
          name: appointmentData.patient_name,
          email: appointmentData.patient_email
        }
      }
    });

    if (error) {
      console.error('Payment failed:', error);
      alert('Payment failed: ' + error.message);
    } else {
      console.log('Payment succeeded!');
      window.location.href = '/appointment/confirmation';
    }

    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="appointment-details">
        <h3>Consultation with Dr. {appointmentData.doctor_name}</h3>
        <p>Date: {appointmentData.appointment_time}</p>
        <p>Fee: ${appointmentData.consultation_fee}</p>
      </div>
      
      <div className="payment-section">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': { color: '#aab7c4' }
              }
            }
          }}
        />
      </div>
      
      <button type="submit" disabled={!stripe || loading}>
        {loading ? 'Processing...' : `Pay $${appointmentData.consultation_fee}`}
      </button>
    </form>
  );
}

function App() {
  const appointmentData = {
    doctor_id: 'doc_123',
    patient_id: 'pat_456',
    appointment_id: 'apt_789',
    consultation_fee: 100,
    doctor_name: 'Jane Smith',
    patient_name: 'John Doe',
    patient_email: '<EMAIL>',
    appointment_time: '2024-01-15 10:00 AM'
  };

  return (
    <Elements stripe={stripePromise}>
      <PaymentForm appointmentData={appointmentData} />
    </Elements>
  );
}

export default App;
```

---

## 🏥 Healthcare-Specific Examples

### **Doctor Onboarding Flow**

```javascript
// Complete doctor onboarding implementation
class DoctorOnboarding {
  async createDoctorAccount(doctorData) {
    try {
      // 1. Create Stripe Express account
      const account = await stripe.accounts.create({
        type: 'express',
        country: doctorData.country || 'US',
        email: doctorData.email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true }
        },
        business_type: 'individual',
        individual: {
          first_name: doctorData.firstName,
          last_name: doctorData.lastName,
          email: doctorData.email,
          phone: doctorData.phone
        },
        business_profile: {
          mcc: '8011', // Medical practitioners
          product_description: 'Medical consultation services',
          support_email: doctorData.email,
          support_phone: doctorData.phone,
          url: `https://yourplatform.com/doctor/${doctorData.id}`
        },
        metadata: {
          doctor_id: doctorData.id,
          specialty: doctorData.specialty,
          license_number: doctorData.licenseNumber,
          platform: 'healthcare_ai'
        }
      });

      // 2. Save account ID to database
      await this.saveDoctorStripeAccount(doctorData.id, account.id);

      // 3. Generate onboarding link
      const accountLink = await stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${process.env.FRONTEND_URL}/doctor/stripe-refresh`,
        return_url: `${process.env.FRONTEND_URL}/doctor/stripe-success`,
        type: 'account_onboarding'
      });

      return {
        account_id: account.id,
        onboarding_url: accountLink.url,
        status: 'pending_verification'
      };

    } catch (error) {
      console.error('Doctor account creation failed:', error);
      throw new Error('Failed to create doctor account');
    }
  }

  async verifyMedicalLicense(doctorData) {
    // Integration with medical license verification service
    try {
      const verificationResult = await this.callLicenseVerificationAPI({
        license_number: doctorData.licenseNumber,
        state: doctorData.licenseState,
        last_name: doctorData.lastName
      });

      return {
        verified: verificationResult.status === 'active',
        license_status: verificationResult.status,
        expiration_date: verificationResult.expiration,
        disciplinary_actions: verificationResult.disciplinary_actions || [],
        verification_date: new Date()
      };

    } catch (error) {
      console.error('License verification failed:', error);
      return {
        verified: false,
        error: 'Verification service unavailable',
        requires_manual_review: true
      };
    }
  }

  async checkAccountStatus(doctorId) {
    const doctor = await this.getDoctorById(doctorId);
    
    if (!doctor.stripe_account_id) {
      return { status: 'not_connected' };
    }

    try {
      const account = await stripe.accounts.retrieve(doctor.stripe_account_id);
      
      return {
        status: account.details_submitted ? 'active' : 'pending',
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        requirements: account.requirements,
        verification_status: account.individual?.verification?.status
      };
    } catch (error) {
      console.error('Error checking account status:', error);
      return { status: 'error', error: error.message };
    }
  }
}
```

### **HIPAA-Compliant Payment Processing**

```javascript
// HIPAA-compliant payment metadata handling
class HIPAACompliantPayments {
  sanitizePaymentMetadata(appointmentData) {
    // Remove any PHI (Protected Health Information)
    return {
      // Safe: Non-PHI identifiers
      appointment_id: appointmentData.id,
      doctor_id: appointmentData.doctor_id,
      patient_id: appointmentData.patient_id, // Internal ID, not SSN
      
      // Safe: General service information
      service_type: 'telemedicine_consultation',
      duration_minutes: appointmentData.duration,
      consultation_category: this.generalizeSpecialty(appointmentData.doctor_specialty),
      
      // Safe: Business information
      platform_fee_percentage: 2,
      payment_method_type: 'card',
      
      // Avoid: Specific medical information
      // diagnosis: appointmentData.diagnosis, // ❌ PHI
      // symptoms: appointmentData.symptoms,   // ❌ PHI
      // treatment: appointmentData.treatment  // ❌ PHI
    };
  }

  generalizeSpecialty(specialty) {
    const generalCategories = {
      'cardiology': 'specialist_consultation',
      'dermatology': 'specialist_consultation',
      'psychiatry': 'mental_health_consultation',
      'oncology': 'specialist_consultation',
      'general_practice': 'general_consultation'
    };
    
    return generalCategories[specialty] || 'medical_consultation';
  }

  async createHIPAACompliantPayment(appointmentData) {
    // Sanitize metadata to remove PHI
    const safeMetadata = this.sanitizePaymentMetadata(appointmentData);
    
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(appointmentData.consultation_fee * 100),
      currency: 'usd',
      application_fee_amount: Math.round(appointmentData.consultation_fee * 100 * 0.02),
      transfer_data: {
        destination: appointmentData.doctor_stripe_account
      },
      metadata: safeMetadata
    });

    // Log payment creation (no PHI in logs)
    await this.logPaymentEvent('payment_intent_created', {
      payment_intent_id: paymentIntent.id,
      appointment_id: appointmentData.id,
      amount: appointmentData.consultation_fee,
      doctor_specialty_category: safeMetadata.consultation_category
    });

    return paymentIntent;
  }
}
```

---

## 🔔 Webhook Implementation Examples

### **Comprehensive Webhook Handler**

```javascript
// Production-ready webhook handler
class WebhookHandler {
  constructor() {
    this.handlers = {
      'payment_intent.succeeded': this.handleSuccessfulPayment.bind(this),
      'payment_intent.payment_failed': this.handleFailedPayment.bind(this),
      'account.updated': this.handleAccountUpdate.bind(this),
      'transfer.created': this.handleTransferCreated.bind(this),
      'transfer.paid': this.handleTransferPaid.bind(this),
      'payout.created': this.handlePayoutCreated.bind(this)
    };
  }

  async processWebhook(req, res) {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body, 
        sig, 
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.log(`Webhook signature verification failed:`, err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Check if event already processed (idempotency)
    const existingProcess = await this.getProcessedEvent(event.id);
    if (existingProcess) {
      console.log(`Event ${event.id} already processed`);
      return res.json({ received: true, status: 'already_processed' });
    }

    try {
      // Process the event
      const handler = this.handlers[event.type];
      if (handler) {
        await handler(event.data.object);
        await this.markEventProcessed(event.id);
      } else {
        console.log(`Unhandled event type: ${event.type}`);
      }

      res.json({ received: true });
    } catch (error) {
      console.error(`Error processing webhook ${event.type}:`, error);
      await this.logWebhookError(event.id, event.type, error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  async handleSuccessfulPayment(paymentIntent) {
    const { appointment_id, doctor_id, patient_id } = paymentIntent.metadata;
    
    try {
      // 1. Create confirmed appointment
      const appointment = await this.createAppointment({
        id: appointment_id,
        doctor_id: doctor_id,
        patient_id: patient_id,
        status: 'confirmed',
        payment_status: 'paid',
        payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount / 100
      });

      // 2. Send notifications
      await Promise.all([
        this.sendPatientConfirmation(appointment),
        this.sendDoctorNotification(appointment),
        this.updateDoctorAvailability(doctor_id, appointment.appointment_time)
      ]);

      // 3. Log successful transaction
      await this.logTransaction('appointment_confirmed', {
        appointment_id: appointment_id,
        payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount / 100
      });

    } catch (error) {
      console.error('Error handling successful payment:', error);
      // Consider implementing retry logic or manual review queue
      await this.flagForManualReview('payment_processing_error', {
        payment_intent_id: paymentIntent.id,
        error: error.message
      });
    }
  }

  async handleFailedPayment(paymentIntent) {
    const { appointment_id } = paymentIntent.metadata;
    
    try {
      // 1. Release reserved time slot
      await this.releaseTimeSlot(appointment_id);
      
      // 2. Notify patient of payment failure
      await this.notifyPatientPaymentFailed(appointment_id, paymentIntent.last_payment_error);
      
      // 3. Log failed transaction
      await this.logTransaction('payment_failed', {
        appointment_id: appointment_id,
        payment_intent_id: paymentIntent.id,
        error_code: paymentIntent.last_payment_error?.code
      });

    } catch (error) {
      console.error('Error handling failed payment:', error);
    }
  }

  async handleAccountUpdate(account) {
    const doctorId = account.metadata?.doctor_id;
    
    if (!doctorId) {
      console.log('Account update for non-doctor account:', account.id);
      return;
    }

    try {
      // Update doctor's account status
      await this.updateDoctorAccountStatus(doctorId, {
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        details_submitted: account.details_submitted,
        requirements: account.requirements
      });

      // Notify doctor if action required
      if (account.requirements.currently_due.length > 0) {
        await this.notifyDoctorActionRequired(doctorId, account.requirements);
      }

      // Enable doctor profile if account is ready
      if (account.charges_enabled && account.payouts_enabled) {
        await this.enableDoctorProfile(doctorId);
        await this.notifyDoctorAccountReady(doctorId);
      }

    } catch (error) {
      console.error('Error handling account update:', error);
    }
  }
}
```

---

## 📊 Analytics & Monitoring Examples

### **Payment Analytics Implementation**

```javascript
// Comprehensive analytics for healthcare marketplace
class PaymentAnalytics {
  async trackPaymentEvent(eventType, eventData) {
    const analyticsEvent = {
      timestamp: new Date(),
      event_type: eventType,
      payment_intent_id: eventData.payment_intent_id,
      amount: eventData.amount,
      currency: eventData.currency || 'usd',
      doctor_specialty: eventData.doctor_specialty,
      patient_location: eventData.patient_location,
      payment_method_type: eventData.payment_method_type,
      processing_time: eventData.processing_time
    };
    
    // Send to analytics service (e.g., Google Analytics, Mixpanel)
    await this.sendToAnalytics(analyticsEvent);
    
    // Update real-time metrics
    await this.updateRealTimeMetrics(eventType, eventData);
  }

  async generatePlatformReport(timeframe = '30d') {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));

    const metrics = await this.calculateMetrics(startDate);
    
    return {
      // Revenue metrics
      total_revenue: metrics.total_amount,
      platform_commission: metrics.total_commission,
      doctor_earnings: metrics.total_amount - metrics.total_commission,
      
      // Transaction metrics
      transaction_count: metrics.transaction_count,
      success_rate: metrics.success_rate,
      average_transaction_size: metrics.average_amount,
      
      // User metrics
      active_doctors: metrics.active_doctors,
      active_patients: metrics.active_patients,
      
      // Specialty breakdown
      specialty_distribution: metrics.specialty_breakdown,
      
      // Geographic distribution
      geographic_distribution: metrics.location_breakdown,
      
      // Payment method preferences
      payment_method_breakdown: metrics.payment_method_breakdown
    };
  }

  async getDoctorPerformanceMetrics(doctorId, timeframe = '30d') {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));

    const doctorMetrics = await this.calculateDoctorMetrics(doctorId, startDate);
    
    return {
      total_consultations: doctorMetrics.consultation_count,
      total_earnings: doctorMetrics.total_earnings,
      average_consultation_fee: doctorMetrics.average_fee,
      patient_retention_rate: doctorMetrics.retention_rate,
      average_rating: doctorMetrics.average_rating,
      availability_utilization: doctorMetrics.utilization_rate
    };
  }
}
```

---

## 🧪 Testing Examples

### **Unit Tests for Payment Processing**

```javascript
// Jest tests for payment processing
describe('Healthcare Payment Processing', () => {
  let paymentProcessor;
  let mockStripe;

  beforeEach(() => {
    mockStripe = {
      paymentIntents: {
        create: jest.fn(),
        retrieve: jest.fn()
      },
      accounts: {
        create: jest.fn(),
        retrieve: jest.fn()
      }
    };
    
    paymentProcessor = new PaymentProcessor(mockStripe);
  });

  describe('createConsultationPayment', () => {
    test('should create payment intent with correct commission', async () => {
      const appointmentData = {
        doctor_id: 'doc_123',
        patient_id: 'pat_456',
        consultation_fee: 100,
        appointment_id: 'apt_789'
      };

      const mockPaymentIntent = {
        id: 'pi_test123',
        client_secret: 'pi_test123_secret_test',
        amount: 10000,
        application_fee_amount: 200
      };

      mockStripe.paymentIntents.create.mockResolvedValue(mockPaymentIntent);

      const result = await paymentProcessor.createConsultationPayment(appointmentData);

      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
        amount: 10000, // $100 in cents
        currency: 'usd',
        application_fee_amount: 200, // 2% commission
        transfer_data: {
          destination: expect.any(String)
        },
        metadata: {
          appointment_id: 'apt_789',
          doctor_id: 'doc_123',
          patient_id: 'pat_456',
          consultation_fee: '100'
        }
      });

      expect(result.client_secret).toBe('pi_test123_secret_test');
    });

    test('should handle doctor account not found', async () => {
      const appointmentData = {
        doctor_id: 'invalid_doctor',
        consultation_fee: 100
      };

      await expect(
        paymentProcessor.createConsultationPayment(appointmentData)
      ).rejects.toThrow('Doctor not found or payment setup incomplete');
    });
  });

  describe('webhook processing', () => {
    test('should handle successful payment webhook', async () => {
      const webhookEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test123',
            metadata: {
              appointment_id: 'apt_789',
              doctor_id: 'doc_123',
              patient_id: 'pat_456'
            }
          }
        }
      };

      const createAppointmentSpy = jest.spyOn(paymentProcessor, 'createAppointment');
      createAppointmentSpy.mockResolvedValue({ id: 'apt_789' });

      await paymentProcessor.handleWebhook(webhookEvent);

      expect(createAppointmentSpy).toHaveBeenCalledWith({
        id: 'apt_789',
        doctor_id: 'doc_123',
        patient_id: 'pat_456',
        status: 'confirmed',
        payment_status: 'paid',
        payment_intent_id: 'pi_test123'
      });
    });
  });
});
```

---

## 🚀 Deployment Examples

### **Production Environment Configuration**

```javascript
// Production-ready server configuration
const express = require('express');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cors = require('cors');

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "https://js.stripe.com"],
      frameSrc: ["'self'", "https://js.stripe.com"],
      connectSrc: ["'self'", "https://api.stripe.com"]
    }
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Body parsing
app.use('/webhook', express.raw({ type: 'application/json' }));
app.use(express.json({ limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    error: 'Internal server error',
    ...(isDevelopment && { details: error.message, stack: error.stack })
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Healthcare marketplace server running on port ${PORT}`);
});
```

---

**These code examples provide a solid foundation for implementing your healthcare marketplace. Use them as starting points and customize based on your specific requirements!**

**Next**: [Testing & Debugging →](../07-testing-debugging/README.md)
