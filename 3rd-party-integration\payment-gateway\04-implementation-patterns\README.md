# Implementation Patterns

## 🛠️ Common Use Cases & Implementation Strategies

This section provides proven patterns for implementing payment systems across different business models and use cases, with special focus on marketplace and healthcare applications.

---

## 📁 Section Contents

### **Core Patterns**
- [`marketplace-payments.md`](./marketplace-payments.md) - Multi-party payment splitting and commission handling
- [`subscription-billing.md`](./subscription-billing.md) - Recurring payments and subscription management
- [`e-commerce-checkout.md`](./e-commerce-checkout.md) - Standard online payment processing
- [`mobile-payments.md`](./mobile-payments.md) - Mobile app payment integration

### **Advanced Features**
- [`webhooks-events.md`](./webhooks-events.md) - Event-driven architecture and webhook handling
- [`fraud-prevention.md`](./fraud-prevention.md) - Security and fraud detection implementation
- [`international-payments.md`](./international-payments.md) - Multi-currency and global payment support
- [`payment-methods.md`](./payment-methods.md) - Supporting multiple payment types

### **Healthcare Specific**
- [`healthcare-marketplace.md`](./healthcare-marketplace.md) - Doctor-patient payment flows
- [`telemedicine-billing.md`](./telemedicine-billing.md) - Remote consultation payment patterns
- [`healthcare-subscriptions.md`](./healthcare-subscriptions.md) - Recurring healthcare service billing

---

## 🎯 Pattern Selection Guide

### **Choose Your Implementation Pattern**

```
Business Model → Recommended Pattern → Key Features
├── Single Vendor E-commerce → Standard Checkout → Simple payment processing
├── Marketplace Platform → Multi-party Payments → Commission splitting
├── SaaS/Subscription → Recurring Billing → Automated renewals
├── Healthcare Platform → Healthcare Marketplace → Compliance + Marketplace
├── Mobile App → Mobile Payments → Native SDK integration
└── Global Business → International Payments → Multi-currency support
```

### **Your Healthcare Platform Pattern**

**Primary Pattern**: Healthcare Marketplace
- **Base**: Marketplace payments with commission splitting
- **Enhanced**: Healthcare compliance and verification
- **Features**: Doctor onboarding, patient payments, automated payouts
- **Compliance**: HIPAA, medical licensing, telemedicine regulations

---

## 🏥 Healthcare Marketplace Implementation

### **Complete Payment Flow Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Patient     │    │   Your Platform │    │     Doctor      │
│                 │    │                 │    │                 │
│ 1. Books Appt   │───▶│ 2. Creates PI   │    │ 5. Gets Notified│
│ 3. Pays $100    │    │ 4. Takes $2 Fee │    │ 6. Receives $98 │
│ 7. Gets Confirm │◀───│ 8. Confirms Appt│───▶│ 9. Provides Care│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Stripe Payment  │    │ Platform Wallet │    │ Doctor Account  │
│ Processing      │    │ (Commission)    │    │ (Service Fee)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Implementation Steps**

**Step 1: Doctor Onboarding**
```javascript
// Create Express account for doctor
const doctorAccount = await stripe.accounts.create({
  type: 'express',
  country: 'US',
  email: doctorData.email,
  capabilities: {
    card_payments: { requested: true },
    transfers: { requested: true }
  },
  business_type: 'individual',
  individual: {
    first_name: doctorData.firstName,
    last_name: doctorData.lastName,
    email: doctorData.email
  },
  metadata: {
    doctor_id: doctorData.id,
    specialty: doctorData.specialty
  }
});
```

**Step 2: Payment Processing**
```javascript
// Process consultation payment with commission
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000, // $100 consultation
  currency: 'usd',
  application_fee_amount: 200, // $2 platform fee (2%)
  transfer_data: {
    destination: doctorAccount.id
  },
  metadata: {
    appointment_id: appointmentId,
    doctor_id: doctorId,
    patient_id: patientId
  }
});
```

**Step 3: Webhook Handling**
```javascript
// Confirm appointment after successful payment
app.post('/webhook', (req, res) => {
  const event = req.body;
  
  if (event.type === 'payment_intent.succeeded') {
    const { appointment_id } = event.data.object.metadata;
    
    // Create confirmed appointment
    createAppointment(appointment_id);
    
    // Notify all parties
    notifyPatient(appointment_id);
    notifyDoctor(appointment_id);
  }
  
  res.json({ received: true });
});
```

---

## 💡 Common Implementation Patterns

### **Pattern 1: Direct Payment Processing**

**Use Case**: Simple e-commerce, single vendor
**Complexity**: Low
**Implementation Time**: 1-2 weeks

```javascript
// Basic payment processing
const paymentIntent = await stripe.paymentIntents.create({
  amount: 2000, // $20.00
  currency: 'usd',
  metadata: { order_id: '12345' }
});

// Frontend confirmation
const {error} = await stripe.confirmCardPayment(clientSecret, {
  payment_method: { card: cardElement }
});
```

**Pros**: Simple, fast implementation
**Cons**: Limited to single-party payments

### **Pattern 2: Marketplace with Commission**

**Use Case**: Multi-vendor platforms, service marketplaces
**Complexity**: Medium
**Implementation Time**: 3-6 weeks

```javascript
// Marketplace payment with automatic commission
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000,
  currency: 'usd',
  application_fee_amount: 500, // 5% commission
  transfer_data: {
    destination: sellerAccount
  }
});
```

**Pros**: Automatic commission handling, seller payouts
**Cons**: Requires Connect setup, more complex onboarding

### **Pattern 3: Subscription Billing**

**Use Case**: SaaS, recurring services, membership sites
**Complexity**: Medium-High
**Implementation Time**: 4-8 weeks

```javascript
// Recurring subscription
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: [{ price: priceId }],
  payment_behavior: 'default_incomplete',
  expand: ['latest_invoice.payment_intent']
});
```

**Pros**: Automated recurring billing, dunning management
**Cons**: Complex pricing models, subscription lifecycle management

### **Pattern 4: On-Demand Services**

**Use Case**: Ride-sharing, food delivery, healthcare consultations
**Complexity**: High
**Implementation Time**: 6-12 weeks

```javascript
// On-demand service with dynamic pricing
const paymentIntent = await stripe.paymentIntents.create({
  amount: calculateDynamicPrice(serviceData),
  currency: 'usd',
  application_fee_amount: calculateCommission(serviceData),
  transfer_data: {
    destination: serviceProviderAccount
  },
  metadata: {
    service_id: serviceData.id,
    provider_id: serviceData.providerId,
    customer_id: serviceData.customerId
  }
});
```

**Pros**: Flexible pricing, real-time transactions
**Cons**: Complex business logic, real-time processing requirements

---

## 🔄 Event-Driven Architecture

### **Webhook-Based Implementation**

**Core Principle**: Never trust client-side payment confirmations
**Implementation**: Use webhooks for all business logic

```javascript
// Webhook-driven appointment confirmation
const webhookHandlers = {
  'payment_intent.succeeded': async (paymentIntent) => {
    // Payment successful - safe to fulfill service
    const { appointment_id, doctor_id, patient_id } = paymentIntent.metadata;
    
    // Create appointment
    const appointment = await createAppointment({
      id: appointment_id,
      doctor_id: doctor_id,
      patient_id: patient_id,
      status: 'confirmed',
      payment_status: 'paid'
    });
    
    // Send confirmations
    await sendPatientConfirmation(appointment);
    await sendDoctorNotification(appointment);
    
    // Update availability
    await updateDoctorAvailability(doctor_id, appointment.time);
  },
  
  'payment_intent.payment_failed': async (paymentIntent) => {
    // Payment failed - release reserved resources
    const { appointment_id } = paymentIntent.metadata;
    
    await releaseTimeSlot(appointment_id);
    await notifyPatientPaymentFailed(appointment_id);
  },
  
  'transfer.paid': async (transfer) => {
    // Doctor payout completed
    const doctorId = transfer.metadata.doctor_id;
    await notifyDoctorPayoutCompleted(doctorId, transfer);
  }
};
```

### **Idempotency and Reliability**

```javascript
// Ensure webhook processing is idempotent
class WebhookProcessor {
  async processWebhook(event) {
    // Check if already processed
    const existingProcess = await this.getProcessedEvent(event.id);
    if (existingProcess) {
      console.log(`Event ${event.id} already processed`);
      return { status: 'already_processed' };
    }
    
    try {
      // Process the event
      const result = await this.handleEvent(event);
      
      // Mark as processed
      await this.markEventProcessed(event.id, result);
      
      return result;
    } catch (error) {
      // Log error and mark for retry
      await this.logWebhookError(event.id, error);
      throw error;
    }
  }
  
  async handleEvent(event) {
    const handler = webhookHandlers[event.type];
    if (!handler) {
      console.log(`No handler for event type: ${event.type}`);
      return { status: 'no_handler' };
    }
    
    return await handler(event.data.object);
  }
}
```

---

## 🔐 Security Implementation Patterns

### **Secure Payment Form Pattern**

```html
<!-- Secure payment form with Stripe Elements -->
<form id="payment-form">
  <!-- Stripe handles card data -->
  <div id="card-element"></div>
  
  <!-- Your business data -->
  <input type="hidden" name="appointment_id" value="apt_123">
  <input type="hidden" name="doctor_id" value="doc_456">
  
  <button type="submit" id="submit-button">
    Pay $100
  </button>
</form>

<script>
const stripe = Stripe('pk_test_...');
const elements = stripe.elements();
const cardElement = elements.create('card');
cardElement.mount('#card-element');

document.getElementById('payment-form').addEventListener('submit', async (event) => {
  event.preventDefault();
  
  // Get client secret from your server
  const {clientSecret} = await fetch('/create-payment-intent', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      appointment_id: document.querySelector('[name="appointment_id"]').value,
      doctor_id: document.querySelector('[name="doctor_id"]').value
    })
  }).then(r => r.json());
  
  // Confirm payment
  const {error} = await stripe.confirmCardPayment(clientSecret, {
    payment_method: {card: cardElement}
  });
  
  if (error) {
    console.error('Payment failed:', error);
  } else {
    console.log('Payment succeeded!');
    window.location.href = '/appointment/confirmation';
  }
});
</script>
```

### **Backend Security Pattern**

```javascript
// Secure backend implementation
app.post('/create-payment-intent', async (req, res) => {
  try {
    // Validate and sanitize input
    const { appointment_id, doctor_id } = req.body;
    
    if (!appointment_id || !doctor_id) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Verify appointment exists and is available
    const appointment = await getAppointment(appointment_id);
    if (!appointment || appointment.status !== 'pending') {
      return res.status(400).json({ error: 'Invalid appointment' });
    }
    
    // Verify doctor account
    const doctor = await getDoctor(doctor_id);
    if (!doctor || !doctor.stripe_account_id) {
      return res.status(400).json({ error: 'Doctor not available' });
    }
    
    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: appointment.fee * 100,
      currency: 'usd',
      application_fee_amount: Math.round(appointment.fee * 100 * 0.02),
      transfer_data: {
        destination: doctor.stripe_account_id
      },
      metadata: {
        appointment_id: appointment_id,
        doctor_id: doctor_id,
        patient_id: req.user.id // From authentication
      }
    });
    
    res.json({ clientSecret: paymentIntent.client_secret });
    
  } catch (error) {
    console.error('Payment intent creation failed:', error);
    res.status(500).json({ error: 'Payment setup failed' });
  }
});
```

---

## 📊 Performance Optimization Patterns

### **Caching Strategy**

```javascript
// Cache frequently accessed data
class PaymentCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 minutes
  }
  
  async getDoctorAccount(doctorId) {
    const cacheKey = `doctor_account_${doctorId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }
    
    const doctorAccount = await this.fetchDoctorAccount(doctorId);
    this.cache.set(cacheKey, {
      data: doctorAccount,
      timestamp: Date.now()
    });
    
    return doctorAccount;
  }
  
  invalidateDoctor(doctorId) {
    this.cache.delete(`doctor_account_${doctorId}`);
  }
}
```

### **Async Processing Pattern**

```javascript
// Handle time-consuming operations asynchronously
class AsyncPaymentProcessor {
  async processPayment(paymentData) {
    // Quick response to user
    const paymentIntent = await stripe.paymentIntents.create({
      amount: paymentData.amount,
      currency: 'usd',
      metadata: paymentData.metadata
    });
    
    // Queue background tasks
    await this.queueBackgroundTasks(paymentIntent.id, paymentData);
    
    return { clientSecret: paymentIntent.client_secret };
  }
  
  async queueBackgroundTasks(paymentIntentId, paymentData) {
    // Queue email notifications
    await this.queue.add('send_confirmation_email', {
      paymentIntentId,
      patientId: paymentData.patient_id
    });
    
    // Queue availability updates
    await this.queue.add('update_availability', {
      doctorId: paymentData.doctor_id,
      appointmentTime: paymentData.appointment_time
    });
    
    // Queue analytics tracking
    await this.queue.add('track_analytics', {
      event: 'payment_initiated',
      paymentIntentId
    });
  }
}
```

---

## 🧪 Testing Patterns

### **Payment Testing Strategy**

```javascript
// Comprehensive payment testing
describe('Healthcare Payment Flow', () => {
  beforeEach(async () => {
    // Set up test data
    this.testDoctor = await createTestDoctor();
    this.testPatient = await createTestPatient();
    this.testAppointment = await createTestAppointment();
  });
  
  test('successful consultation payment', async () => {
    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      appointment_id: this.testAppointment.id,
      doctor_id: this.testDoctor.id,
      amount: 10000
    });
    
    // Simulate successful payment
    await stripe.paymentIntents.confirm(paymentIntent.id, {
      payment_method: 'pm_card_visa'
    });
    
    // Verify appointment created
    const appointment = await getAppointment(this.testAppointment.id);
    expect(appointment.status).toBe('confirmed');
    expect(appointment.payment_status).toBe('paid');
  });
  
  test('failed payment handling', async () => {
    // Simulate declined payment
    const paymentIntent = await createPaymentIntent({
      appointment_id: this.testAppointment.id,
      doctor_id: this.testDoctor.id,
      amount: 10000
    });
    
    try {
      await stripe.paymentIntents.confirm(paymentIntent.id, {
        payment_method: 'pm_card_chargeDeclined'
      });
    } catch (error) {
      // Verify error handling
      expect(error.type).toBe('card_error');
    }
    
    // Verify appointment not created
    const appointment = await getAppointment(this.testAppointment.id);
    expect(appointment.status).toBe('pending');
  });
});
```

---

## 📈 Monitoring and Analytics Patterns

### **Payment Analytics Implementation**

```javascript
// Track payment metrics and performance
class PaymentAnalytics {
  async trackPaymentEvent(eventType, eventData) {
    const analyticsEvent = {
      timestamp: new Date(),
      event_type: eventType,
      payment_intent_id: eventData.payment_intent_id,
      amount: eventData.amount,
      currency: eventData.currency,
      doctor_specialty: eventData.doctor_specialty,
      patient_location: eventData.patient_location,
      payment_method_type: eventData.payment_method_type,
      processing_time: eventData.processing_time
    };
    
    // Send to analytics service
    await this.sendToAnalytics(analyticsEvent);
    
    // Update real-time metrics
    await this.updateMetrics(eventType, eventData);
  }
  
  async generatePaymentReport(timeframe) {
    const metrics = await this.getPaymentMetrics(timeframe);
    
    return {
      total_volume: metrics.total_amount,
      transaction_count: metrics.transaction_count,
      success_rate: metrics.success_rate,
      average_transaction_size: metrics.average_amount,
      top_specialties: metrics.specialty_breakdown,
      geographic_distribution: metrics.location_breakdown,
      payment_method_preferences: metrics.payment_method_breakdown
    };
  }
}
```

---

**Implementation patterns provide the foundation for building robust payment systems. Next, let's dive into your specific healthcare marketplace implementation!**

**Next**: [Healthcare Marketplace Implementation →](../05-healthcare-marketplace/README.md)
