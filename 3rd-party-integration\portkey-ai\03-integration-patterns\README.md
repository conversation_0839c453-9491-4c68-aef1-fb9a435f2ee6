# Integration Patterns

## 🔄 Common Use Cases & Implementation Strategies

Explore proven patterns for integrating Portkey.ai into different types of AI applications, from simple chatbots to complex multi-model systems.

---

## 📁 Section Contents

### **Core Patterns**
- [`llm-routing.md`](./llm-routing.md) - Intelligent model selection and routing strategies
- [`fallback-strategies.md`](./fallback-strategies.md) - Reliability patterns and error handling
- [`caching-patterns.md`](./caching-patterns.md) - Performance optimization through caching
- [`rate-limiting.md`](./rate-limiting.md) - Usage control and quota management

### **Application Patterns**
- [`chatbot-integration.md`](./chatbot-integration.md) - Conversational AI implementation patterns
- [`content-generation.md`](./content-generation.md) - Text and content creation workflows
- [`code-generation.md`](./code-generation.md) - Programming assistance and automation
- [`data-analysis.md`](./data-analysis.md) - Document processing and insights

### **Advanced Patterns**
- [`multi-model-workflows.md`](./multi-model-workflows.md) - Complex workflows using multiple models
- [`cost-optimization.md`](./cost-optimization.md) - Strategies for minimizing AI costs
- [`prompt-engineering.md`](./prompt-engineering.md) - Advanced prompt management patterns
- [`observability-patterns.md`](./observability-patterns.md) - Monitoring and analytics implementation

---

## 🎯 Pattern Selection Guide

### **Choose Your Integration Pattern**

```
Application Type → Recommended Pattern → Key Features
├── Simple Chatbot → Basic Routing → Single model with fallback
├── Enterprise Chat → Multi-Model → Load balancing + fallbacks
├── Content Platform → Cost-Optimized → Route by content type
├── Code Assistant → Specialized Routing → Model selection by task
├── Data Analysis → Batch Processing → Parallel model execution
└── Multi-Tenant SaaS → Tenant Isolation → Per-tenant configs
```

### **Pattern Complexity Levels**

**Level 1: Basic Integration**
- Single model with simple fallback
- Basic caching and rate limiting
- Standard observability

**Level 2: Production Ready**
- Multi-model routing strategies
- Advanced caching patterns
- Comprehensive monitoring

**Level 3: Enterprise Scale**
- Complex workflow orchestration
- Custom routing algorithms
- Advanced cost optimization

---

## 🚀 Quick Start Patterns

### **Pattern 1: Simple Chat Application**

**Use Case**: Basic chatbot with reliability
**Complexity**: Low
**Implementation Time**: 1-2 hours

```javascript
// Basic chat with fallback
const chatConfig = {
  strategy: { mode: "fallback" },
  targets: [
    { provider: "openai", model: "gpt-4" },
    { provider: "anthropic", model: "claude-2" },
    { provider: "openai", model: "gpt-3.5-turbo" }
  ],
  cache: { mode: "simple", ttl: 300 },
  retry: { max_attempts: 3, backoff: "exponential" }
};

const response = await portkey.chat.completions.create({
  config: chatConfig,
  messages: [
    { role: "system", content: "You are a helpful assistant" },
    { role: "user", content: userMessage }
  ]
});
```

### **Pattern 2: Cost-Optimized Content Generation**

**Use Case**: High-volume content creation
**Complexity**: Medium
**Implementation Time**: 4-6 hours

```javascript
// Route by content complexity
const contentConfig = {
  strategy: { mode: "conditional" },
  rules: [
    {
      condition: "request.tokens < 500",
      target: { provider: "openai", model: "gpt-3.5-turbo" }
    },
    {
      condition: "request.complexity === 'high'",
      target: { provider: "openai", model: "gpt-4" }
    },
    {
      condition: "request.type === 'creative'",
      target: { provider: "anthropic", model: "claude-2" }
    }
  ],
  default: { provider: "openai", model: "gpt-3.5-turbo" },
  cache: { mode: "semantic", ttl: 3600, similarity_threshold: 0.9 },
  budget: { limit: 100, period: "daily" }
};
```

### **Pattern 3: Multi-Model Code Assistant**

**Use Case**: Programming help with specialized models
**Complexity**: High
**Implementation Time**: 1-2 days

```javascript
// Specialized routing for code tasks
const codeAssistantConfig = {
  strategy: { mode: "conditional" },
  rules: [
    {
      condition: "request.language === 'python'",
      target: { provider: "openai", model: "gpt-4", temperature: 0.1 }
    },
    {
      condition: "request.task === 'code_review'",
      target: { provider: "anthropic", model: "claude-2", temperature: 0.2 }
    },
    {
      condition: "request.task === 'debugging'",
      target: { provider: "openai", model: "gpt-4", temperature: 0.0 }
    },
    {
      condition: "request.task === 'documentation'",
      target: { provider: "openai", model: "gpt-3.5-turbo", temperature: 0.3 }
    }
  ],
  fallbacks: [
    { provider: "openai", model: "gpt-4" },
    { provider: "anthropic", model: "claude-2" }
  ],
  cache: { mode: "simple", ttl: 1800 }
};
```

---

## 🔄 Routing Strategies Deep Dive

### **Load Balancing Patterns**

**Round Robin**: Equal distribution across models
```javascript
const roundRobinConfig = {
  strategy: { mode: "round_robin" },
  targets: [
    { provider: "openai", model: "gpt-4" },
    { provider: "anthropic", model: "claude-2" },
    { provider: "google", model: "palm-2" }
  ],
  health_check: { enabled: true, interval: 30 }
};
```

**Weighted Distribution**: Proportional traffic allocation
```javascript
const weightedConfig = {
  strategy: { mode: "weighted" },
  targets: [
    { provider: "openai", model: "gpt-4", weight: 50 },      // 50%
    { provider: "anthropic", model: "claude-2", weight: 30 }, // 30%
    { provider: "openai", model: "gpt-3.5-turbo", weight: 20 } // 20%
  ]
};
```

**Performance-Based**: Route to fastest available model
```javascript
const performanceConfig = {
  strategy: { mode: "performance_optimized" },
  targets: [
    { provider: "openai", model: "gpt-4", avg_latency: 1200 },
    { provider: "anthropic", model: "claude-2", avg_latency: 800 },
    { provider: "openai", model: "gpt-3.5-turbo", avg_latency: 600 }
  ],
  latency_threshold: 2000,
  update_interval: 60 // Update performance metrics every minute
};
```

### **Intelligent Routing Patterns**

**Content-Based Routing**: Route based on request content
```javascript
const contentBasedConfig = {
  strategy: { mode: "content_based" },
  rules: [
    {
      content_type: "creative_writing",
      target: { provider: "anthropic", model: "claude-2" }
    },
    {
      content_type: "technical_documentation",
      target: { provider: "openai", model: "gpt-4" }
    },
    {
      content_type: "casual_conversation",
      target: { provider: "openai", model: "gpt-3.5-turbo" }
    }
  ],
  content_classifier: {
    model: "text-classification-model",
    confidence_threshold: 0.8
  }
};
```

**User-Based Routing**: Route based on user characteristics
```javascript
const userBasedConfig = {
  strategy: { mode: "user_based" },
  rules: [
    {
      user_tier: "premium",
      target: { provider: "openai", model: "gpt-4" }
    },
    {
      user_tier: "standard",
      target: { provider: "anthropic", model: "claude-2" }
    },
    {
      user_tier: "free",
      target: { provider: "openai", model: "gpt-3.5-turbo" }
    }
  ],
  rate_limits: {
    premium: { requests: 1000, tokens: 100000 },
    standard: { requests: 500, tokens: 50000 },
    free: { requests: 100, tokens: 10000 }
  }
};
```

---

## 💾 Caching Optimization Patterns

### **Hierarchical Caching**

**Multi-Level Cache Strategy**:
```javascript
const hierarchicalCacheConfig = {
  levels: [
    {
      name: "L1_memory",
      type: "memory",
      ttl: 300,        // 5 minutes
      max_size: "100MB",
      eviction: "lru"
    },
    {
      name: "L2_redis",
      type: "redis",
      ttl: 3600,       // 1 hour
      max_size: "1GB",
      cluster: true
    },
    {
      name: "L3_database",
      type: "database",
      ttl: 86400,      // 24 hours
      compression: true
    }
  ],
  cache_key_strategy: "content_hash_with_context"
};
```

### **Semantic Caching Patterns**

**Context-Aware Semantic Caching**:
```javascript
const semanticCacheConfig = {
  mode: "semantic",
  embedding_model: "text-embedding-ada-002",
  similarity_threshold: 0.95,
  context_aware: true,
  context_keys: ["user_id", "session_id", "domain"],
  cache_warming: {
    enabled: true,
    popular_queries: true,
    schedule: "0 2 * * *" // Daily at 2 AM
  },
  invalidation: {
    time_based: { ttl: 3600 },
    event_based: ["model_update", "prompt_change"],
    manual: true
  }
};
```

---

## 🛡️ Reliability Patterns

### **Circuit Breaker Implementation**

**Advanced Circuit Breaker Pattern**:
```javascript
const circuitBreakerConfig = {
  failure_threshold: 5,        // Open after 5 failures
  success_threshold: 3,        // Close after 3 successes
  timeout: 30000,             // 30 second timeout
  half_open_max_calls: 3,     // Max calls in half-open state
  
  failure_conditions: [
    "timeout",
    "rate_limit_exceeded", 
    "server_error",
    "model_unavailable"
  ],
  
  fallback_strategy: {
    type: "cached_response",
    max_age: 3600,
    default_response: "I'm experiencing technical difficulties. Please try again later."
  },
  
  monitoring: {
    metrics: ["failure_rate", "response_time", "success_rate"],
    alerts: {
      circuit_open: { webhook: "https://alerts.yourapp.com/circuit-open" },
      high_failure_rate: { threshold: 0.1, action: "notify_team" }
    }
  }
};
```

### **Retry Strategies**

**Intelligent Retry Pattern**:
```javascript
const retryConfig = {
  max_attempts: 3,
  backoff_strategy: "exponential_jitter",
  base_delay: 1000,           // 1 second
  max_delay: 30000,           // 30 seconds
  jitter: 0.1,                // 10% jitter
  
  retryable_conditions: [
    { error: "timeout", max_attempts: 3 },
    { error: "rate_limit", max_attempts: 5, backoff: "linear" },
    { error: "server_error", max_attempts: 2 },
    { error: "network_error", max_attempts: 4 }
  ],
  
  non_retryable_conditions: [
    "authentication_error",
    "invalid_request",
    "content_policy_violation"
  ]
};
```

---

## 📊 Observability Integration Patterns

### **Comprehensive Monitoring**

**Multi-Dimensional Observability**:
```javascript
const observabilityConfig = {
  metrics: {
    business: [
      "user_satisfaction_score",
      "feature_usage_rate", 
      "conversion_rate"
    ],
    technical: [
      "request_latency",
      "error_rate",
      "throughput",
      "cache_hit_rate"
    ],
    cost: [
      "cost_per_request",
      "cost_per_user",
      "budget_utilization"
    ]
  },
  
  logging: {
    level: "info",
    structured: true,
    sampling_rate: 0.1,        // Log 10% of requests
    sensitive_data_masking: true,
    retention_days: 30
  },
  
  tracing: {
    enabled: true,
    sample_rate: 0.01,         // Trace 1% of requests
    include_request_body: false,
    include_response_body: false
  },
  
  alerts: [
    {
      name: "high_error_rate",
      condition: "error_rate > 0.05",
      window: "5m",
      action: "notify_team"
    },
    {
      name: "high_latency",
      condition: "p95_latency > 3000",
      window: "10m", 
      action: "scale_up"
    },
    {
      name: "budget_exceeded",
      condition: "daily_cost > budget_limit",
      action: "throttle_requests"
    }
  ]
};
```

---

## 🎯 Application-Specific Patterns

### **Chatbot Pattern**

**Conversational AI with Context Management**:
```javascript
class ChatbotPattern {
  constructor() {
    this.config = {
      strategy: { mode: "fallback" },
      targets: [
        { provider: "openai", model: "gpt-4" },
        { provider: "anthropic", model: "claude-2" }
      ],
      context_management: {
        max_history: 10,
        context_window: 4000,
        summarization: {
          enabled: true,
          trigger_length: 3000,
          summary_model: "gpt-3.5-turbo"
        }
      },
      cache: {
        mode: "semantic",
        ttl: 1800,
        similarity_threshold: 0.92
      }
    };
  }

  async processMessage(message, conversationId) {
    // Get conversation context
    const context = await this.getConversationContext(conversationId);
    
    // Build messages with context
    const messages = this.buildMessagesWithContext(context, message);
    
    // Process with Portkey
    const response = await portkey.chat.completions.create({
      config: this.config,
      messages: messages,
      metadata: {
        conversation_id: conversationId,
        user_id: context.userId,
        feature: "chatbot"
      }
    });
    
    // Update conversation context
    await this.updateConversationContext(conversationId, message, response);
    
    return response;
  }
}
```

### **Content Generation Pattern**

**Scalable Content Creation Pipeline**:
```javascript
class ContentGenerationPattern {
  constructor() {
    this.configs = {
      blog_posts: {
        strategy: { mode: "cost_optimized" },
        targets: [
          { provider: "anthropic", model: "claude-2", cost_weight: 0.8 },
          { provider: "openai", model: "gpt-4", cost_weight: 0.2 }
        ],
        cache: { mode: "semantic", ttl: 7200 }
      },
      
      social_media: {
        strategy: { mode: "performance_optimized" },
        targets: [
          { provider: "openai", model: "gpt-3.5-turbo" }
        ],
        cache: { mode: "simple", ttl: 3600 }
      },
      
      technical_docs: {
        strategy: { mode: "quality_optimized" },
        targets: [
          { provider: "openai", model: "gpt-4" },
          { provider: "anthropic", model: "claude-2" }
        ],
        cache: { mode: "semantic", ttl: 86400 }
      }
    };
  }

  async generateContent(contentType, prompt, options = {}) {
    const config = this.configs[contentType];
    
    const response = await portkey.chat.completions.create({
      config: config,
      messages: [
        { role: "system", content: this.getSystemPrompt(contentType) },
        { role: "user", content: prompt }
      ],
      temperature: options.creativity || 0.7,
      max_tokens: options.maxLength || 1000,
      metadata: {
        content_type: contentType,
        user_id: options.userId,
        feature: "content_generation"
      }
    });
    
    return this.postProcessContent(response, contentType);
  }
}
```

---

## 💰 Cost Optimization Patterns

### **Dynamic Budget Management**

**Adaptive Cost Control**:
```javascript
const budgetConfig = {
  daily_budget: 100,
  monthly_budget: 2500,
  
  cost_tiers: [
    {
      threshold: 0.8,           // 80% of budget
      action: "route_to_cheaper_models",
      models: ["gpt-3.5-turbo", "claude-instant"]
    },
    {
      threshold: 0.9,           // 90% of budget
      action: "enable_aggressive_caching",
      cache_ttl: 7200
    },
    {
      threshold: 0.95,          // 95% of budget
      action: "throttle_requests",
      rate_limit: 0.5           // 50% of normal rate
    },
    {
      threshold: 1.0,           // 100% of budget
      action: "emergency_mode",
      fallback: "cached_responses_only"
    }
  ],
  
  optimization_strategies: [
    "prompt_compression",
    "response_truncation", 
    "batch_processing",
    "off_peak_scheduling"
  ]
};
```

---

**These integration patterns provide proven approaches for building robust, scalable AI applications with Portkey.ai. Ready to dive into implementation details?**

**Next**: [Implementation Guide →](../04-implementation-guide/README.md)
