#!/bin/bash

# Git Workflow Helper Script
# Automates common Git workflows for cross-functional teams

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEFAULT_MAIN_BRANCH="main"
TEAM_DISCIPLINES=("backend" "frontend" "mobile" "devops" "shared")
BRANCH_TYPES=("feature" "bugfix" "hotfix" "refactor" "docs" "test" "chore")

# Helper functions
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if we're in a Git repository
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a Git repository"
        exit 1
    fi
}

# Get current branch name
get_current_branch() {
    git symbolic-ref --short HEAD 2>/dev/null || git rev-parse --short HEAD
}

# Check if branch exists
branch_exists() {
    git show-ref --verify --quiet refs/heads/"$1"
}

# Validate branch name format
validate_branch_name() {
    local branch_name="$1"
    if [[ ! $branch_name =~ ^(feature|bugfix|hotfix|refactor|docs|test|chore)/(backend|frontend|mobile|devops|shared)/[a-z0-9-]+$ ]]; then
        print_error "Invalid branch name format. Use: type/scope/description"
        echo "Examples:"
        echo "  feature/backend/user-authentication"
        echo "  bugfix/frontend/login-validation"
        echo "  hotfix/mobile/crash-fix"
        return 1
    fi
    return 0
}

# Start new feature workflow
start_feature() {
    print_header "Starting New Feature"
    
    # Get feature details
    echo "Available branch types: ${BRANCH_TYPES[*]}"
    read -p "Branch type: " branch_type
    
    echo "Available disciplines: ${TEAM_DISCIPLINES[*]}"
    read -p "Discipline/scope: " scope
    
    read -p "Feature description (kebab-case): " description
    
    # Construct branch name
    branch_name="${branch_type}/${scope}/${description}"
    
    # Validate branch name
    if ! validate_branch_name "$branch_name"; then
        exit 1
    fi
    
    # Check if branch already exists
    if branch_exists "$branch_name"; then
        print_error "Branch '$branch_name' already exists"
        exit 1
    fi
    
    # Ensure we're on main and up to date
    print_header "Preparing workspace"
    git checkout "$DEFAULT_MAIN_BRANCH"
    git pull origin "$DEFAULT_MAIN_BRANCH"
    
    # Create and switch to new branch
    git checkout -b "$branch_name"
    git push -u origin "$branch_name"
    
    print_success "Created and switched to branch: $branch_name"
    
    # Create feature outline file
    cat > FEATURE.md << EOF
# ${description^} Feature

## Description
Brief description of the feature and its purpose.

## Scope
- [ ] Backend changes
- [ ] Frontend changes  
- [ ] Mobile changes
- [ ] DevOps/Infrastructure changes
- [ ] Documentation updates

## Dependencies
List any dependencies on other teams or features.

## Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

## Testing Strategy
- [ ] Unit tests
- [ ] Integration tests
- [ ] E2E tests
- [ ] Manual testing

## Rollout Plan
Describe how this feature will be deployed and rolled out.
EOF
    
    git add FEATURE.md
    git commit -m "${branch_type}(${scope}): add ${description} feature outline"
    git push origin "$branch_name"
    
    print_success "Feature outline created and committed"
    print_warning "Don't forget to create a pull request when ready for review!"
}

# Sync with main branch
sync_with_main() {
    print_header "Syncing with Main Branch"
    
    local current_branch
    current_branch=$(get_current_branch)
    
    if [ "$current_branch" = "$DEFAULT_MAIN_BRANCH" ]; then
        print_warning "Already on main branch"
        git pull origin "$DEFAULT_MAIN_BRANCH"
        return
    fi
    
    # Fetch latest changes
    git fetch origin
    
    # Check for uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        print_warning "You have uncommitted changes. Stashing them..."
        git stash push -m "Auto-stash before sync with main"
        local stashed=true
    fi
    
    # Rebase onto main
    print_header "Rebasing onto main"
    if git rebase origin/"$DEFAULT_MAIN_BRANCH"; then
        print_success "Successfully rebased onto main"
    else
        print_error "Rebase conflicts detected. Resolve conflicts and run 'git rebase --continue'"
        exit 1
    fi
    
    # Restore stashed changes if any
    if [ "$stashed" = true ]; then
        print_header "Restoring stashed changes"
        git stash pop
        print_success "Stashed changes restored"
    fi
    
    # Force push the rebased branch
    git push --force-with-lease origin "$current_branch"
    print_success "Branch synced and pushed to remote"
}

# Clean up merged branches
cleanup_branches() {
    print_header "Cleaning Up Merged Branches"
    
    # Fetch latest changes
    git fetch origin --prune
    
    # Switch to main branch
    git checkout "$DEFAULT_MAIN_BRANCH"
    git pull origin "$DEFAULT_MAIN_BRANCH"
    
    # Find merged branches
    local merged_branches
    merged_branches=$(git branch --merged "$DEFAULT_MAIN_BRANCH" | grep -v "$DEFAULT_MAIN_BRANCH" | grep -v "^\*" | xargs)
    
    if [ -z "$merged_branches" ]; then
        print_success "No merged branches to clean up"
        return
    fi
    
    echo "Merged branches found:"
    echo "$merged_branches"
    
    read -p "Delete these branches? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "$merged_branches" | xargs git branch -d
        print_success "Merged branches deleted"
    else
        print_warning "Branch cleanup cancelled"
    fi
}

# Create pull request template
create_pr_template() {
    print_header "Creating Pull Request"
    
    local current_branch
    current_branch=$(get_current_branch)
    
    if [ "$current_branch" = "$DEFAULT_MAIN_BRANCH" ]; then
        print_error "Cannot create PR from main branch"
        exit 1
    fi
    
    # Check if there are commits to push
    local commits_ahead
    commits_ahead=$(git rev-list --count origin/"$DEFAULT_MAIN_BRANCH"..HEAD)
    
    if [ "$commits_ahead" -eq 0 ]; then
        print_error "No commits to create PR for"
        exit 1
    fi
    
    # Generate PR description
    local pr_title
    pr_title=$(git log --format="%s" -n 1)
    
    cat > PR_TEMPLATE.md << EOF
## Description
Brief description of the changes and their purpose.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Changes Made
- Change 1
- Change 2
- Change 3

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All tests passing

## Dependencies
List any dependencies on other PRs or external changes.

## Screenshots/Demo
Include relevant screenshots or demo links if applicable.

## Checklist
- [ ] Code follows team style guidelines
- [ ] Self-review of code completed
- [ ] Documentation updated
- [ ] Breaking changes documented
- [ ] Security considerations addressed

## Related Issues
Closes #issue_number
EOF
    
    print_success "PR template created: PR_TEMPLATE.md"
    print_warning "Edit the template and create your pull request on GitHub/GitLab"
    
    # Show GitHub/GitLab URL if possible
    local remote_url
    remote_url=$(git config --get remote.origin.url)
    if [[ $remote_url =~ github\.com ]]; then
        local repo_path
        repo_path=$(echo "$remote_url" | sed 's/.*github\.com[:/]\(.*\)\.git/\1/')
        echo "GitHub PR URL: https://github.com/$repo_path/compare/$current_branch"
    elif [[ $remote_url =~ gitlab\.com ]]; then
        local repo_path
        repo_path=$(echo "$remote_url" | sed 's/.*gitlab\.com[:/]\(.*\)\.git/\1/')
        echo "GitLab MR URL: https://gitlab.com/$repo_path/-/merge_requests/new?merge_request[source_branch]=$current_branch"
    fi
}

# Repository health check
health_check() {
    print_header "Repository Health Check"
    
    # Check repository size
    local repo_size
    repo_size=$(du -sh .git | cut -f1)
    echo "Repository size: $repo_size"
    
    # Check for large files
    local large_files
    large_files=$(git rev-list --objects --all | \
        git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
        awk '/^blob/ {if($3 > 10485760) print $4, $3}' | \
        head -5)
    
    if [ -n "$large_files" ]; then
        print_warning "Large files detected (>10MB):"
        echo "$large_files"
    else
        print_success "No large files detected"
    fi
    
    # Check for untracked files
    local untracked_files
    untracked_files=$(git ls-files --others --exclude-standard)
    
    if [ -n "$untracked_files" ]; then
        print_warning "Untracked files found:"
        echo "$untracked_files" | head -10
    else
        print_success "No untracked files"
    fi
    
    # Check branch status
    local current_branch
    current_branch=$(get_current_branch)
    local commits_ahead
    local commits_behind
    
    if [ "$current_branch" != "$DEFAULT_MAIN_BRANCH" ]; then
        commits_ahead=$(git rev-list --count origin/"$DEFAULT_MAIN_BRANCH"..HEAD 2>/dev/null || echo "0")
        commits_behind=$(git rev-list --count HEAD..origin/"$DEFAULT_MAIN_BRANCH" 2>/dev/null || echo "0")
        
        echo "Current branch: $current_branch"
        echo "Commits ahead of main: $commits_ahead"
        echo "Commits behind main: $commits_behind"
        
        if [ "$commits_behind" -gt 0 ]; then
            print_warning "Branch is behind main. Consider syncing."
        fi
    fi
    
    # Check for merge conflicts
    if git ls-files -u | grep -q .; then
        print_error "Merge conflicts detected"
        git status --porcelain | grep "^UU"
    else
        print_success "No merge conflicts"
    fi
}

# Main menu
show_menu() {
    echo
    print_header "Git Workflow Helper"
    echo "1. Start new feature"
    echo "2. Sync with main branch"
    echo "3. Clean up merged branches"
    echo "4. Create PR template"
    echo "5. Repository health check"
    echo "6. Exit"
    echo
}

# Main script
main() {
    check_git_repo
    
    if [ $# -eq 0 ]; then
        # Interactive mode
        while true; do
            show_menu
            read -p "Choose an option (1-6): " choice
            
            case $choice in
                1) start_feature ;;
                2) sync_with_main ;;
                3) cleanup_branches ;;
                4) create_pr_template ;;
                5) health_check ;;
                6) print_success "Goodbye!"; exit 0 ;;
                *) print_error "Invalid option" ;;
            esac
            
            echo
            read -p "Press Enter to continue..."
        done
    else
        # Command line mode
        case $1 in
            start|feature) start_feature ;;
            sync) sync_with_main ;;
            cleanup) cleanup_branches ;;
            pr) create_pr_template ;;
            health) health_check ;;
            *) 
                echo "Usage: $0 [start|sync|cleanup|pr|health]"
                exit 1
                ;;
        esac
    fi
}

# Run main function
main "$@"
