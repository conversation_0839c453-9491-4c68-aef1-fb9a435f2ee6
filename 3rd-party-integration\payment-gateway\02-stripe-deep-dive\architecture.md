# Stripe Architecture & Core APIs

## 🏗️ Understanding Stripe's Technical Foundation

Stripe's architecture is designed for developer productivity, security, and global scale. This guide explains how all the pieces fit together.

---

## 🌐 High-Level Architecture

### **Stripe's Core Components**

```
┌─────────────────────────────────────────────────────────────────┐
│                        Stripe Platform                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Frontend      │   Backend APIs  │      Infrastructure        │
│                 │                 │                             │
│ • Stripe.js     │ • Payment APIs  │ • Global Data Centers      │
│ • Elements      │ • Connect APIs  │ • PCI Compliance           │
│ • Mobile SDKs   │ • Billing APIs  │ • Fraud Detection          │
│ • Checkout      │ • Webhooks      │ • Monitoring & Analytics   │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### **Data Flow Architecture**

```
Customer → Stripe.js → Your Backend → Stripe API → Banking Network
    │         │           │            │              │
    ▼         ▼           ▼            ▼              ▼
 Payment   Tokenize   Create PI    Process        Settlement
  Form      Card      + Metadata   Payment        & Transfer
```

---

## 🔑 Core API Concepts

### **RESTful Design Principles**

**Predictable URLs:**
```
GET    /v1/customers           # List customers
POST   /v1/customers           # Create customer
GET    /v1/customers/{id}      # Retrieve customer
POST   /v1/customers/{id}      # Update customer
DELETE /v1/customers/{id}      # Delete customer
```

**Consistent Response Format:**
```json
{
  "object": "payment_intent",
  "id": "pi_1234567890",
  "amount": 2000,
  "currency": "usd",
  "status": "succeeded",
  "created": **********,
  "metadata": {}
}
```

**Error Handling:**
```json
{
  "error": {
    "type": "card_error",
    "code": "card_declined",
    "message": "Your card was declined.",
    "param": "payment_method"
  }
}
```

### **Authentication & Security**

**API Key Types:**
```bash
# Publishable Key (Frontend)
pk_test_51234567890abcdef...  # Safe to expose in client-side code

# Secret Key (Backend)
sk_test_51234567890abcdef...  # Must be kept secure on server

# Restricted Key (Specific Permissions)
rk_test_51234567890abcdef...  # Limited scope for specific operations
```

**Request Authentication:**
```javascript
// All API requests require authentication
const stripe = require('stripe')('sk_test_...');

// Requests are automatically authenticated
const customer = await stripe.customers.create({
  email: '<EMAIL>'
});
```

---

## 💳 Payment Processing Architecture

### **Modern Approach: Payment Intents**

**Payment Intent Lifecycle:**
```
Created → Requires Payment Method → Processing → Succeeded
   │              │                     │          │
   ▼              ▼                     ▼          ▼
Setup PI    Attach Method         Confirm PI   Complete
```

**Implementation Pattern:**
```javascript
// 1. Create Payment Intent (Backend)
const paymentIntent = await stripe.paymentIntents.create({
  amount: 2000,
  currency: 'usd',
  metadata: { order_id: '12345' }
});

// 2. Confirm Payment (Frontend)
const {error} = await stripe.confirmCardPayment(
  paymentIntent.client_secret,
  {
    payment_method: {
      card: cardElement,
      billing_details: { name: 'Customer Name' }
    }
  }
);
```

### **Legacy Approach: Charges API**

**Direct Charge (Not Recommended):**
```javascript
// Old approach - less flexible
const charge = await stripe.charges.create({
  amount: 2000,
  currency: 'usd',
  source: 'tok_visa'
});
```

**Why Payment Intents are Better:**
- **3D Secure Support**: Automatic handling of authentication
- **Retry Logic**: Built-in failure recovery
- **Webhook Reliability**: Consistent event delivery
- **Future-Proof**: Supports new payment methods automatically

---

## 🔗 Key Object Relationships

### **Core Objects Hierarchy**

```
Customer
├── Payment Methods (Cards, Bank Accounts)
├── Subscriptions
│   └── Subscription Items
│       └── Prices
└── Invoices
    └── Payment Intents

Payment Intent
├── Payment Method
├── Charges
└── Transfer Data (for Connect)
```

### **Healthcare Marketplace Example**

```javascript
// Your platform's object relationships
const patient = await stripe.customers.create({
  email: '<EMAIL>',
  metadata: { patient_id: 'pat_123' }
});

const doctor = await stripe.accounts.create({
  type: 'express',
  email: '<EMAIL>',
  metadata: { doctor_id: 'doc_456' }
});

const consultation = await stripe.paymentIntents.create({
  amount: 10000, // $100
  currency: 'usd',
  customer: patient.id,
  application_fee_amount: 200, // $2 commission
  transfer_data: { destination: doctor.id },
  metadata: {
    appointment_id: 'apt_789',
    consultation_type: 'video_call'
  }
});
```

---

## 🌍 Global Infrastructure

### **Data Centers & Regions**

**Primary Regions:**
- **US East (Virginia)**: Primary data center
- **US West (California)**: Backup and load balancing
- **Europe (Ireland)**: EU data residency
- **Asia-Pacific (Singapore)**: Regional processing

**Data Residency:**
```javascript
// Automatic routing based on account country
const stripe = require('stripe')('sk_test_...', {
  apiVersion: '2022-11-15',
  stripeAccount: 'acct_eu_account' // Routes to EU infrastructure
});
```

### **Performance Characteristics**

**API Response Times:**
- **P50**: <100ms (50th percentile)
- **P95**: <200ms (95th percentile)
- **P99**: <500ms (99th percentile)

**Availability:**
- **SLA**: 99.99% uptime
- **Actual**: 99.995% historical uptime
- **Redundancy**: Multi-region failover

---

## 🔄 Event-Driven Architecture

### **Webhook System**

**Event Flow:**
```
Stripe Event → Webhook Endpoint → Your Application → Business Logic
     │              │                    │               │
     ▼              ▼                    ▼               ▼
  payment_      HTTP POST           Process Event    Update DB
  intent_       with Event          & Verify         & Notify
  succeeded     Payload             Signature        Users
```

**Webhook Implementation:**
```javascript
// Webhook endpoint
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      // Create appointment in your system
      await createAppointment(event.data.object);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({received: true});
});
```

### **Critical Events for Healthcare Platform**

```javascript
// Essential webhook events for your use case
const criticalEvents = [
  'payment_intent.succeeded',      // Payment completed
  'payment_intent.payment_failed', // Payment failed
  'account.updated',               // Doctor account changes
  'transfer.created',              // Doctor payout initiated
  'invoice.payment_succeeded',     // Subscription payment
  'customer.subscription.deleted'  // Subscription cancelled
];
```

---

## 🔐 Security Architecture

### **PCI Compliance Layers**

```
┌─────────────────────────────────────────────────────────────┐
│                    PCI Level 1 Compliance                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Data Layer    │  Network Layer  │    Application Layer   │
│                 │                 │                         │
│ • Encryption    │ • TLS 1.2+      │ • Input Validation     │
│ • Tokenization  │ • Network       │ • Access Controls      │
│ • Key Mgmt      │   Segmentation  │ • Audit Logging        │
│ • Data Masking  │ • Firewalls     │ • Vulnerability Mgmt   │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### **Tokenization Flow**

```
Card Number → Stripe.js → Stripe Servers → Token → Your Backend
1234-5678    Encrypt     Tokenize        tok_123   Store Token
9012-3456    in Browser  & Store         (Safe)    (PCI Safe)
```

**Implementation:**
```javascript
// Frontend: Tokenize card (PCI-safe)
const {token, error} = await stripe.createToken(cardElement);

// Backend: Use token (no card data)
const charge = await stripe.charges.create({
  amount: 2000,
  currency: 'usd',
  source: token.id // Token, not raw card data
});
```

---

## 📊 Monitoring & Observability

### **Built-in Monitoring**

**Dashboard Metrics:**
- Transaction success rates
- Payment method performance
- Geographic distribution
- Error rate analysis
- Revenue analytics

**API Monitoring:**
```javascript
// Request IDs for debugging
const paymentIntent = await stripe.paymentIntents.create({
  amount: 2000,
  currency: 'usd'
}, {
  idempotencyKey: 'unique-key-123' // Prevent duplicate requests
});

console.log('Request ID:', paymentIntent.lastResponse.requestId);
```

### **Custom Monitoring Integration**

```javascript
// Add monitoring to your Stripe integration
const stripe = require('stripe')('sk_test_...', {
  telemetry: true, // Enable telemetry
  timeout: 20000,  // 20 second timeout
  maxNetworkRetries: 3
});

// Custom error tracking
stripe.on('request', (request) => {
  console.log('Stripe Request:', request.method, request.url);
});

stripe.on('response', (response) => {
  console.log('Stripe Response:', response.status, response.elapsed);
});
```

---

## 🔧 Development Tools

### **Stripe CLI**

**Installation & Setup:**
```bash
# Install Stripe CLI
brew install stripe/stripe-cli/stripe

# Login to your account
stripe login

# Listen for webhooks
stripe listen --forward-to localhost:3000/webhook
```

**Common Commands:**
```bash
# Test webhook events
stripe trigger payment_intent.succeeded

# View API logs
stripe logs tail

# Create test data
stripe fixtures fixtures.json
```

### **Testing Infrastructure**

**Test Environment:**
```javascript
// Separate test and live environments
const stripe = require('stripe')(
  process.env.NODE_ENV === 'production' 
    ? process.env.STRIPE_LIVE_SECRET_KEY
    : process.env.STRIPE_TEST_SECRET_KEY
);
```

**Test Cards:**
```javascript
// Common test card numbers
const testCards = {
  visa: '****************',
  visaDebit: '****************',
  mastercard: '****************',
  amex: '***************',
  declined: '****************',
  requiresAuth: '****************'
};
```

---

## 🏥 Healthcare Platform Architecture

### **Recommended Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Patient App   │    │  Your Backend   │    │ Doctor Portal   │
│                 │    │                 │    │                 │
│ • Stripe.js     │───▶│ • Stripe APIs   │◀───│ • Account Mgmt  │
│ • Payment Form  │    │ • Webhooks      │    │ • Payout Info   │
│ • Booking UI    │    │ • Business      │    │ • Earnings      │
└─────────────────┘    │   Logic         │    └─────────────────┘
                       └─────────────────┘
                               │
                               ▼
                       ┌─────────────────┐
                       │ Stripe Connect  │
                       │                 │
                       │ • Doctor Accts  │
                       │ • Transfers     │
                       │ • Compliance    │
                       └─────────────────┘
```

### **Key Integration Points**

**1. Patient Payment Flow:**
```javascript
// Create payment with doctor transfer
const paymentIntent = await stripe.paymentIntents.create({
  amount: consultationFee * 100,
  currency: 'usd',
  application_fee_amount: platformFee,
  transfer_data: { destination: doctorAccount },
  metadata: { appointment_id, doctor_id, patient_id }
});
```

**2. Doctor Onboarding:**
```javascript
// Create Express account for doctor
const account = await stripe.accounts.create({
  type: 'express',
  country: 'US',
  email: doctorEmail,
  capabilities: {
    card_payments: { requested: true },
    transfers: { requested: true }
  }
});
```

**3. Webhook Processing:**
```javascript
// Handle successful payments
if (event.type === 'payment_intent.succeeded') {
  const { appointment_id } = event.data.object.metadata;
  await confirmAppointment(appointment_id);
  await notifyDoctor(appointment_id);
  await sendPatientConfirmation(appointment_id);
}
```

---

**Next**: [Account Types →](./account-types.md)
