# AI Gateway Terminology

## 📚 Essential Terms & Concepts

Master the vocabulary used in AI gateways, LLM integration, and Portkey.ai platform discussions.

---

## 🤖 AI & LLM Terms

### **Large Language Model (LLM)**
A type of artificial intelligence model trained on vast amounts of text data to understand and generate human-like text.

**Examples**: GPT-4, Claude<PERSON>2, PaLM, LLaMA

### **Prompt**
The input text or instruction given to an LLM to generate a response.

```javascript
// Example prompt
const prompt = "Write a professional email to a customer about a delayed order";
```

### **Completion**
The text generated by an LLM in response to a prompt.

```javascript
// Completion response
const completion = "Dear Valued Customer, We sincerely apologize for the delay...";
```

### **Token**
The basic unit of text processing in LLMs. Can be words, parts of words, or characters.

```javascript
// Tokenization example
"Hello world" → ["Hello", " world"] // 2 tokens
"AI gateway" → ["AI", " gateway"] // 2 tokens
```

### **Context Window**
The maximum number of tokens an LLM can process in a single request (input + output).

**Examples**:
- GPT-3.5-turbo: 4,096 tokens
- GPT-4: 8,192 tokens (standard) / 32,768 tokens (extended)
- Claude-2: 100,000 tokens

### **Temperature**
A parameter controlling the randomness/creativity of LLM responses (0.0 = deterministic, 1.0 = very creative).

```javascript
const request = {
  prompt: "Write a story",
  temperature: 0.7 // Balanced creativity
};
```

### **Top-p (Nucleus Sampling)**
A parameter controlling response diversity by considering only the most likely tokens.

```javascript
const request = {
  prompt: "Complete this sentence",
  top_p: 0.9 // Consider top 90% probability mass
};
```

---

## 🌐 Gateway & Infrastructure Terms

### **AI Gateway**
An intermediary service that manages and routes requests between applications and AI/LLM providers.

### **Virtual Key**
A Portkey.ai abstraction that maps to real API keys from multiple LLM providers, enabling unified access.

```javascript
// Virtual key usage
const portkey = new Portkey({
  apiKey: "portkey-api-key",
  virtualKey: "vk_unified_access_123" // Maps to OpenAI, Anthropic, etc.
});
```

### **Config**
A configuration object defining how requests should be processed, including routing rules, fallbacks, and settings.

```json
{
  "strategy": { "mode": "fallback" },
  "targets": [
    { "provider": "openai", "model": "gpt-4" },
    { "provider": "anthropic", "model": "claude-2" }
  ]
}
```

### **Provider**
An AI/LLM service company that offers API access to their models.

**Examples**: OpenAI, Anthropic, Google, Cohere, Hugging Face

### **Endpoint**
A specific API URL or service point for accessing LLM functionality.

```javascript
// Different endpoints
const endpoints = {
  chat: "/v1/chat/completions",
  completion: "/v1/completions", 
  embeddings: "/v1/embeddings"
};
```

---

## 🔄 Routing & Load Balancing Terms

### **Load Balancing**
Distributing requests across multiple models or providers to optimize performance and reliability.

```javascript
// Load balancing config
const config = {
  strategy: "weighted",
  targets: [
    { provider: "openai", model: "gpt-4", weight: 70 },
    { provider: "anthropic", model: "claude-2", weight: 30 }
  ]
};
```

### **Fallback**
An alternative model or provider used when the primary option fails or is unavailable.

```javascript
// Fallback chain
const fallbacks = [
  { provider: "openai", model: "gpt-4" },        // Primary
  { provider: "anthropic", model: "claude-2" },  // Fallback 1
  { provider: "openai", model: "gpt-3.5-turbo" } // Fallback 2
];
```

### **Circuit Breaker**
A design pattern that prevents requests to a failing service, allowing it time to recover.

**States**:
- **Closed**: Normal operation, requests pass through
- **Open**: Service failing, requests blocked
- **Half-Open**: Testing if service has recovered

### **Retry Logic**
Automatic re-attempt of failed requests with intelligent backoff strategies.

```javascript
const retryConfig = {
  maxRetries: 3,
  backoffStrategy: "exponential", // 1s, 2s, 4s
  retryableErrors: ["timeout", "rate_limit", "server_error"]
};
```

### **Health Check**
Monitoring mechanism to determine if a provider or model is functioning properly.

---

## 💾 Caching Terms

### **Cache Hit**
When a requested response is found in the cache and returned without calling the LLM.

### **Cache Miss**
When a requested response is not found in the cache, requiring a new LLM call.

### **Time To Live (TTL)**
The duration for which a cached response remains valid before expiring.

```javascript
const cacheConfig = {
  ttl: 3600, // 1 hour in seconds
  mode: "simple"
};
```

### **Semantic Caching**
Caching based on semantic similarity rather than exact text matches.

```javascript
// Semantic cache example
const cached = "What is the capital of France?";
const similar = "What's the capital city of France?"; // 96% similarity → cache hit
const different = "What is the population of France?"; // 60% similarity → cache miss
```

### **Cache Key**
A unique identifier used to store and retrieve cached responses.

```javascript
// Simple cache key
const key = hash(prompt + model + parameters);

// Contextual cache key  
const key = hash(prompt + model + userId + sessionId);
```

---

## 🔐 Security & Authentication Terms

### **API Key**
A secret token used to authenticate requests to AI services.

```javascript
// API key usage
const headers = {
  'Authorization': 'Bearer sk-your-api-key-here',
  'Content-Type': 'application/json'
};
```

### **Rate Limiting**
Controlling the number of requests a user or application can make within a specific time period.

```javascript
const rateLimits = {
  requests: { limit: 1000, window: "1h" },
  tokens: { limit: 100000, window: "1d" },
  cost: { limit: 50, window: "1d" }
};
```

### **Content Moderation**
Filtering or blocking inappropriate, harmful, or policy-violating content.

```javascript
const moderation = {
  categories: ["hate", "violence", "sexual", "self-harm"],
  action: "block" // or "flag", "warn"
};
```

### **Quota**
The maximum amount of usage (requests, tokens, or cost) allowed within a time period.

### **Throttling**
Deliberately slowing down or limiting requests to prevent system overload.

---

## 📊 Observability & Analytics Terms

### **Latency**
The time taken to process a request from start to finish.

```javascript
const metrics = {
  latency: {
    p50: 800,  // 50th percentile (median)
    p95: 1500, // 95th percentile
    p99: 3000  // 99th percentile
  }
};
```

### **Throughput**
The number of requests processed per unit of time.

```javascript
const throughput = {
  requests_per_second: 45,
  tokens_per_minute: 50000
};
```

### **Error Rate**
The percentage of requests that result in errors.

```javascript
const errorRate = {
  total_requests: 10000,
  failed_requests: 50,
  error_rate: 0.5 // 0.5%
};
```

### **Trace**
A record of a request's journey through the system, including all steps and timing.

```json
{
  "trace_id": "trace_123456",
  "spans": [
    { "name": "gateway_processing", "duration_ms": 50 },
    { "name": "provider_call", "duration_ms": 1200 },
    { "name": "response_processing", "duration_ms": 30 }
  ]
}
```

### **Metrics**
Quantitative measurements of system performance and usage.

### **Logs**
Detailed records of events and activities within the system.

---

## 💰 Cost & Billing Terms

### **Token Pricing**
Cost structure based on the number of tokens processed.

```javascript
const pricing = {
  input_tokens: 0.03, // per 1K tokens
  output_tokens: 0.06 // per 1K tokens
};
```

### **Usage-Based Billing**
Pricing model where costs are based on actual usage (tokens, requests, etc.).

### **Budget**
A spending limit set to control costs.

```javascript
const budget = {
  limit: 100, // $100
  period: "monthly",
  alerts: [50, 80, 95] // Alert at 50%, 80%, 95%
};
```

### **Cost Per Request**
The average cost of processing a single request.

```javascript
const costAnalysis = {
  total_cost: 125.50,
  total_requests: 5000,
  cost_per_request: 0.0251 // $0.0251 per request
};
```

---

## 🛠️ Development & Integration Terms

### **SDK (Software Development Kit)**
A collection of tools, libraries, and documentation for integrating with a service.

```bash
# Installing SDKs
npm install portkey-ai      # Node.js
pip install portkey-ai      # Python
```

### **REST API**
A web service architecture using HTTP methods for communication.

```javascript
// REST API call
const response = await fetch('https://api.portkey.ai/v1/chat/completions', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer your-api-key' },
  body: JSON.stringify(requestData)
});
```

### **Webhook**
A mechanism for receiving real-time notifications about events.

```javascript
// Webhook endpoint
app.post('/webhook', (req, res) => {
  const event = req.body;
  console.log('Received event:', event.type);
  res.json({ received: true });
});
```

### **Idempotency**
The property that multiple identical requests have the same effect as a single request.

```javascript
const request = {
  messages: messages,
  model: "gpt-4",
  idempotency_key: "unique-key-123" // Ensures single processing
};
```

---

## 🎯 Prompt Engineering Terms

### **Prompt Template**
A reusable prompt structure with placeholders for variables.

```javascript
const template = {
  name: "customer_support",
  template: "You are a helpful customer support agent. Customer: {{message}}",
  variables: ["message"]
};
```

### **Few-Shot Learning**
Providing examples in the prompt to guide the model's responses.

```javascript
const fewShotPrompt = `
Examples:
Q: What is 2+2? A: 4
Q: What is 3+3? A: 6
Q: What is 5+5? A: ?
`;
```

### **Chain of Thought**
Prompting technique that encourages step-by-step reasoning.

```javascript
const chainOfThoughtPrompt = "Let's think step by step to solve this problem...";
```

### **System Message**
Initial instruction that sets the behavior and role for the AI assistant.

```javascript
const systemMessage = {
  role: "system",
  content: "You are a helpful, harmless, and honest AI assistant."
};
```

---

## 📈 Performance & Quality Terms

### **Model Performance**
Measures of how well a model performs on specific tasks.

```javascript
const performance = {
  accuracy: 0.92,
  precision: 0.89,
  recall: 0.94,
  f1_score: 0.91
};
```

### **Response Quality**
Subjective measures of response usefulness and appropriateness.

```javascript
const quality = {
  relevance: 0.95,
  coherence: 0.88,
  helpfulness: 0.91,
  safety: 0.99
};
```

### **Benchmark**
Standardized tests used to evaluate and compare model performance.

**Examples**: MMLU, HellaSwag, HumanEval, BLEU

### **Fine-tuning**
Process of adapting a pre-trained model for specific tasks or domains.

### **Embedding**
Vector representation of text that captures semantic meaning.

```javascript
const embedding = await portkey.embeddings.create({
  input: "Hello world",
  model: "text-embedding-ada-002"
});
// Returns: [0.1, -0.3, 0.7, ...] (1536-dimensional vector)
```

---

## 🔄 Operational Terms

### **Deployment**
The process of making an AI application available for use.

**Types**:
- **Development**: Testing environment
- **Staging**: Pre-production testing
- **Production**: Live user environment

### **Scaling**
Adjusting system capacity to handle varying loads.

**Types**:
- **Horizontal**: Adding more instances
- **Vertical**: Increasing instance capacity

### **Monitoring**
Continuous observation of system health and performance.

### **Alerting**
Automated notifications when issues or thresholds are detected.

```javascript
const alerts = {
  error_rate: { threshold: 5, action: "notify_team" },
  latency: { threshold: 2000, action: "scale_up" },
  cost: { threshold: 100, action: "budget_alert" }
};
```

---

**Understanding this terminology is essential for effective communication about AI gateways and LLM integration. Ready to explore the technical architecture?**

**Next**: [Platform Architecture →](./platform-architecture.md)
