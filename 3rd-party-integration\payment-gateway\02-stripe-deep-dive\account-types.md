# Stripe Account Types & Connect Platform

## 🏦 Understanding Stripe Connect Account Types

Choosing the right account type is crucial for your healthcare marketplace. This guide explains the differences and helps you make the best choice.

---

## 📊 Account Types Overview

### **Three Account Types Comparison**

| Feature | Standard | Express | Custom |
|---------|----------|---------|---------|
| **Onboarding** | Self-service | Simplified | Platform-controlled |
| **Dashboard Access** | Full Stripe dashboard | Limited dashboard | No Stripe dashboard |
| **Branding** | Stripe branding | Mixed branding | Platform branding |
| **Compliance** | Self-managed | Shared responsibility | Platform-managed |
| **Setup Complexity** | Low | Medium | High |
| **Control Level** | Low | Medium | High |
| **Time to Market** | Fast | Medium | Slow |

---

## 🎯 Standard Accounts

### **Overview**
Standard accounts are independent Stripe accounts with a direct relationship to Stripe. Best for established businesses that want full control.

### **Characteristics**
- **Direct Stripe Relationship**: Account holder deals directly with Stripe
- **Full Dashboard Access**: Complete access to Strip<PERSON>'s dashboard and features
- **Independent Compliance**: Account holder responsible for their own compliance
- **Stripe Branding**: Stripe appears in customer communications

### **Use Cases**
- Large, established healthcare providers
- Independent medical practices
- Businesses that want direct Stripe relationship
- Organizations with dedicated finance teams

### **Implementation**
```javascript
// Standard accounts are created independently
// Platform facilitates connection via OAuth
const authUrl = `https://connect.stripe.com/oauth/authorize?` +
  `response_type=code&` +
  `client_id=${STRIPE_CLIENT_ID}&` +
  `scope=read_write&` +
  `redirect_uri=${REDIRECT_URI}`;

// After authorization, exchange code for account access
const response = await stripe.oauth.token({
  grant_type: 'authorization_code',
  code: authorizationCode
});
```

### **Pros & Cons**

**Advantages:**
✅ Full Stripe feature access  
✅ Direct customer support from Stripe  
✅ Independent account management  
✅ No platform dependency  

**Disadvantages:**
❌ Complex onboarding for sellers  
❌ Platform has limited control  
❌ Inconsistent user experience  
❌ Compliance burden on account holder  

---

## ⚡ Express Accounts

### **Overview**
Express accounts provide a simplified onboarding experience while maintaining some Stripe branding. **Recommended for your healthcare marketplace.**

### **Characteristics**
- **Simplified Onboarding**: Streamlined account creation process
- **Limited Dashboard**: Access to essential features only
- **Shared Compliance**: Platform and Stripe share compliance responsibilities
- **Mixed Branding**: Both platform and Stripe branding

### **Perfect for Healthcare Marketplace**
```javascript
// Create Express account for doctor
const account = await stripe.accounts.create({
  type: 'express',
  country: 'US',
  email: '<EMAIL>',
  capabilities: {
    card_payments: { requested: true },
    transfers: { requested: true }
  },
  business_type: 'individual',
  individual: {
    first_name: 'Dr. Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone: '+**********'
  },
  metadata: {
    doctor_id: 'doc_123',
    specialty: 'cardiology',
    platform: 'healthcare_ai'
  }
});

// Generate onboarding link
const accountLink = await stripe.accountLinks.create({
  account: account.id,
  refresh_url: 'https://yourplatform.com/reauth',
  return_url: 'https://yourplatform.com/return',
  type: 'account_onboarding'
});
```

### **Onboarding Flow**
```
Doctor Registration → Express Account Creation → Onboarding Link → 
Stripe Onboarding → Account Verification → Ready to Receive Payments
```

### **Dashboard Features Available**
- Payment history and details
- Payout information and scheduling
- Basic account settings
- Tax document access
- Dispute management

### **Pros & Cons**

**Advantages:**
✅ **Fast Onboarding**: Doctors can start receiving payments quickly  
✅ **Reduced Friction**: Simplified verification process  
✅ **Platform Control**: You maintain control over user experience  
✅ **Shared Compliance**: Stripe helps with regulatory requirements  
✅ **Mobile Optimized**: Great experience on mobile devices  

**Disadvantages:**
❌ Limited dashboard customization  
❌ Some Stripe branding remains  
❌ Fewer advanced features  
❌ Platform dependency for some operations  

---

## 🎨 Custom Accounts

### **Overview**
Custom accounts provide complete white-label experience with full platform control. Best for large platforms with dedicated compliance teams.

### **Characteristics**
- **Full Platform Control**: Complete control over user experience
- **No Stripe Dashboard**: Platform provides all account management
- **Platform Branding**: No Stripe branding visible to users
- **Platform Compliance**: Platform responsible for all compliance

### **Implementation Complexity**
```javascript
// Custom accounts require extensive implementation
const account = await stripe.accounts.create({
  type: 'custom',
  country: 'US',
  email: '<EMAIL>',
  capabilities: {
    card_payments: { requested: true },
    transfers: { requested: true }
  },
  business_type: 'individual',
  individual: {
    first_name: 'Dr. Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone: '+**********',
    ssn_last_4: '1234',
    address: {
      line1: '123 Medical Center Dr',
      city: 'Healthcare City',
      state: 'CA',
      postal_code: '90210',
      country: 'US'
    },
    dob: {
      day: 15,
      month: 6,
      year: 1980
    }
  },
  business_profile: {
    mcc: '8011', // Medical practitioners
    url: 'https://doctorprofile.com'
  },
  tos_acceptance: {
    date: Math.floor(Date.now() / 1000),
    ip: '***********'
  }
});
```

### **Required Platform Features**
- Complete account management interface
- Payment history and analytics
- Payout management and scheduling
- Tax document generation
- Dispute handling interface
- Compliance monitoring and reporting

### **Pros & Cons**

**Advantages:**
✅ **Complete Control**: Full customization of user experience  
✅ **White Label**: No Stripe branding  
✅ **Advanced Features**: Access to all Stripe capabilities  
✅ **Brand Consistency**: Unified platform experience  

**Disadvantages:**
❌ **High Complexity**: Extensive development required  
❌ **Compliance Burden**: Platform responsible for all compliance  
❌ **Longer Development**: Months of additional development  
❌ **Ongoing Maintenance**: Platform must maintain all features  

---

## 🏥 Healthcare Marketplace Recommendation

### **Why Express Accounts are Perfect for Your Platform**

**1. Optimal Balance**
- **Fast Time to Market**: Get doctors onboarded quickly
- **Reduced Development**: Leverage Stripe's onboarding flow
- **Compliance Support**: Stripe handles complex regulatory requirements
- **User Experience**: Good balance of control and simplicity

**2. Doctor Experience**
```javascript
// Simple doctor onboarding flow
async function onboardDoctor(doctorData) {
  // 1. Create Express account
  const account = await stripe.accounts.create({
    type: 'express',
    country: doctorData.country || 'US',
    email: doctorData.email,
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true }
    },
    metadata: {
      doctor_id: doctorData.id,
      specialty: doctorData.specialty
    }
  });

  // 2. Generate onboarding link
  const accountLink = await stripe.accountLinks.create({
    account: account.id,
    refresh_url: `${BASE_URL}/doctor/stripe-refresh`,
    return_url: `${BASE_URL}/doctor/stripe-success`,
    type: 'account_onboarding'
  });

  // 3. Save account ID and redirect doctor
  await updateDoctorStripeAccount(doctorData.id, account.id);
  return accountLink.url;
}
```

**3. Payment Flow**
```javascript
// Process consultation payment with Express account
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000, // $100 consultation
  currency: 'usd',
  application_fee_amount: 200, // $2 platform fee (2%)
  transfer_data: {
    destination: doctorExpressAccount
  },
  metadata: {
    appointment_id: 'apt_123',
    doctor_id: 'doc_456',
    patient_id: 'pat_789'
  }
});
```

---

## 🔄 Account Management

### **Account Status Monitoring**
```javascript
// Check account status and requirements
async function checkAccountStatus(accountId) {
  const account = await stripe.accounts.retrieve(accountId);
  
  return {
    charges_enabled: account.charges_enabled,
    payouts_enabled: account.payouts_enabled,
    details_submitted: account.details_submitted,
    requirements: account.requirements,
    verification_status: account.individual?.verification?.status
  };
}
```

### **Handling Account Updates**
```javascript
// Webhook handler for account updates
app.post('/webhook', (req, res) => {
  const event = req.body;
  
  if (event.type === 'account.updated') {
    const account = event.data.object;
    
    // Update doctor's account status in your database
    updateDoctorAccountStatus(account.id, {
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements
    });
    
    // Notify doctor if action required
    if (account.requirements.currently_due.length > 0) {
      notifyDoctorActionRequired(account.id, account.requirements);
    }
  }
  
  res.json({ received: true });
});
```

### **Re-onboarding for Updates**
```javascript
// Generate new onboarding link for additional information
async function generateUpdateLink(accountId) {
  const accountLink = await stripe.accountLinks.create({
    account: accountId,
    refresh_url: `${BASE_URL}/doctor/stripe-refresh`,
    return_url: `${BASE_URL}/doctor/stripe-success`,
    type: 'account_update'
  });
  
  return accountLink.url;
}
```

---

## 🌍 International Considerations

### **Multi-Country Support**
```javascript
// Support doctors from different countries
const supportedCountries = {
  'US': { currency: 'usd', requirements: ['ssn_last_4'] },
  'CA': { currency: 'cad', requirements: ['sin'] },
  'GB': { currency: 'gbp', requirements: ['sort_code'] },
  'AU': { currency: 'aud', requirements: ['abn'] }
};

async function createInternationalAccount(doctorData) {
  const countryConfig = supportedCountries[doctorData.country];
  
  const account = await stripe.accounts.create({
    type: 'express',
    country: doctorData.country,
    email: doctorData.email,
    default_currency: countryConfig.currency,
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true }
    }
  });
  
  return account;
}
```

### **Currency Handling**
```javascript
// Handle multi-currency payments
const paymentIntent = await stripe.paymentIntents.create({
  amount: convertToMinorUnits(consultationFee, currency),
  currency: currency,
  application_fee_amount: calculatePlatformFee(consultationFee, currency),
  transfer_data: {
    destination: doctorAccount
  }
});
```

---

## 📊 Performance Metrics

### **Account Type Performance Comparison**

| Metric | Standard | Express | Custom |
|--------|----------|---------|---------|
| **Onboarding Time** | 2-7 days | 1-2 days | 3-14 days |
| **Completion Rate** | 60-70% | 80-90% | 70-80% |
| **Development Time** | 1-2 weeks | 2-4 weeks | 2-6 months |
| **Maintenance Effort** | Low | Medium | High |
| **User Satisfaction** | Medium | High | High |

### **Success Metrics for Express Accounts**
- **Onboarding Completion**: >85%
- **Time to First Payment**: <24 hours
- **Account Verification**: >90% within 48 hours
- **Doctor Satisfaction**: >4.5/5

---

## 🚀 Implementation Roadmap

### **Phase 1: Basic Express Account Setup (Week 1-2)**
1. Create Express account creation flow
2. Implement onboarding link generation
3. Set up webhook handling for account updates
4. Build basic account status monitoring

### **Phase 2: Enhanced Features (Week 3-4)**
1. Add re-onboarding for account updates
2. Implement account status dashboard for doctors
3. Set up automated notifications for requirements
4. Add international country support

### **Phase 3: Optimization (Week 5-6)**
1. Optimize onboarding conversion rates
2. Add advanced account analytics
3. Implement automated compliance monitoring
4. Build customer support tools

---

**Express accounts provide the perfect balance for your healthcare marketplace. Next, let's explore payment methods and processing!**

**Next**: [Payment Methods →](./payment-methods.md)
