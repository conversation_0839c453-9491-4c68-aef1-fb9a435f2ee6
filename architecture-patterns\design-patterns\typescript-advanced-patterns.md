# Advanced TypeScript Patterns for Senior Developers

This guide covers advanced TypeScript patterns, type system features, and design patterns that senior developers should master.

## Advanced Type System Features

### 1. Conditional Types

```typescript
// Basic conditional type
type IsString<T> = T extends string ? true : false;

// Distributed conditional types
type ToArray<T> = T extends any ? T[] : never;
type StringOrNumberArray = ToArray<string | number>; // string[] | number[]

// Infer keyword for extracting types
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never;
type ArrayElement<T> = T extends (infer U)[] ? U : never;

// Complex conditional type example
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object
    ? T[P] extends Function
      ? T[P]
      : DeepReadonly<T[P]>
    : T[P];
};
```

### 2. Mapped Types and Template Literal Types

```typescript
// Advanced mapped types
type Getters<T> = {
  [K in keyof T as `get${Capitalize<string & K>}`]: () => T[K];
};

type Setters<T> = {
  [K in keyof T as `set${Capitalize<string & K>}`]: (value: T[K]) => void;
};

// Template literal types for API endpoints
type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';
type Endpoint = '/users' | '/products' | '/orders';
type APIRoute = `${HTTPMethod} ${Endpoint}`;

// Dynamic key generation
type EventMap = {
  click: MouseEvent;
  keydown: KeyboardEvent;
  change: Event;
};

type EventHandlers = {
  [K in keyof EventMap as `on${Capitalize<K>}`]: (event: EventMap[K]) => void;
};
```

### 3. Utility Types and Type Manipulation

```typescript
// Custom utility types
type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

type PickByType<T, U> = {
  [K in keyof T as T[K] extends U ? K : never]: T[K];
};

// Deep partial implementation
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Function overloading with conditional types
function createAPIClient<T extends 'rest' | 'graphql'>(
  type: T
): T extends 'rest' ? RESTClient : GraphQLClient;
function createAPIClient(type: 'rest' | 'graphql') {
  return type === 'rest' ? new RESTClient() : new GraphQLClient();
}
```

## Design Patterns in TypeScript

### 1. Builder Pattern with Fluent Interface

```typescript
class QueryBuilder<T> {
  private conditions: string[] = [];
  private selectFields: string[] = [];
  private orderByField?: string;
  private limitValue?: number;

  select<K extends keyof T>(...fields: K[]): QueryBuilder<Pick<T, K>> {
    this.selectFields = fields as string[];
    return this as any;
  }

  where(condition: string): this {
    this.conditions.push(condition);
    return this;
  }

  orderBy<K extends keyof T>(field: K): this {
    this.orderByField = field as string;
    return this;
  }

  limit(count: number): this {
    this.limitValue = count;
    return this;
  }

  build(): string {
    let query = `SELECT ${this.selectFields.join(', ')} FROM table`;
    
    if (this.conditions.length > 0) {
      query += ` WHERE ${this.conditions.join(' AND ')}`;
    }
    
    if (this.orderByField) {
      query += ` ORDER BY ${this.orderByField}`;
    }
    
    if (this.limitValue) {
      query += ` LIMIT ${this.limitValue}`;
    }
    
    return query;
  }
}

// Usage
interface User {
  id: number;
  name: string;
  email: string;
  age: number;
}

const query = new QueryBuilder<User>()
  .select('name', 'email')
  .where('age > 18')
  .orderBy('name')
  .limit(10)
  .build();
```

### 2. Strategy Pattern with Type Safety

```typescript
// Strategy interface
interface SortStrategy<T> {
  sort(data: T[]): T[];
}

// Concrete strategies
class QuickSort<T> implements SortStrategy<T> {
  constructor(private compareFn: (a: T, b: T) => number) {}
  
  sort(data: T[]): T[] {
    // QuickSort implementation
    return data.sort(this.compareFn);
  }
}

class MergeSort<T> implements SortStrategy<T> {
  constructor(private compareFn: (a: T, b: T) => number) {}
  
  sort(data: T[]): T[] {
    // MergeSort implementation
    return this.mergeSort(data);
  }
  
  private mergeSort(arr: T[]): T[] {
    if (arr.length <= 1) return arr;
    
    const mid = Math.floor(arr.length / 2);
    const left = this.mergeSort(arr.slice(0, mid));
    const right = this.mergeSort(arr.slice(mid));
    
    return this.merge(left, right);
  }
  
  private merge(left: T[], right: T[]): T[] {
    const result: T[] = [];
    let i = 0, j = 0;
    
    while (i < left.length && j < right.length) {
      if (this.compareFn(left[i], right[j]) <= 0) {
        result.push(left[i++]);
      } else {
        result.push(right[j++]);
      }
    }
    
    return result.concat(left.slice(i)).concat(right.slice(j));
  }
}

// Context class
class Sorter<T> {
  constructor(private strategy: SortStrategy<T>) {}
  
  setStrategy(strategy: SortStrategy<T>): void {
    this.strategy = strategy;
  }
  
  sort(data: T[]): T[] {
    return this.strategy.sort(data);
  }
}
```

### 3. Observer Pattern with Type-Safe Events

```typescript
type EventMap = {
  userLogin: { userId: string; timestamp: Date };
  userLogout: { userId: string };
  dataUpdate: { table: string; id: string; data: any };
};

class TypedEventEmitter<T extends Record<string, any>> {
  private listeners: {
    [K in keyof T]?: Array<(data: T[K]) => void>;
  } = {};

  on<K extends keyof T>(event: K, listener: (data: T[K]) => void): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event]!.push(listener);
  }

  off<K extends keyof T>(event: K, listener: (data: T[K]) => void): void {
    const eventListeners = this.listeners[event];
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  emit<K extends keyof T>(event: K, data: T[K]): void {
    const eventListeners = this.listeners[event];
    if (eventListeners) {
      eventListeners.forEach(listener => listener(data));
    }
  }
}

// Usage
const eventEmitter = new TypedEventEmitter<EventMap>();

eventEmitter.on('userLogin', (data) => {
  // data is typed as { userId: string; timestamp: Date }
  console.log(`User ${data.userId} logged in at ${data.timestamp}`);
});

eventEmitter.emit('userLogin', {
  userId: '123',
  timestamp: new Date()
});
```

## Advanced React Patterns with TypeScript

### 1. Higher-Order Components (HOCs) with Generic Constraints

```typescript
interface WithLoadingProps {
  loading: boolean;
}

function withLoading<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P & WithLoadingProps> {
  return (props: P & WithLoadingProps) => {
    if (props.loading) {
      return <div>Loading...</div>;
    }
    return <Component {...props} />;
  };
}

// Usage
interface UserProfileProps {
  user: User;
  onEdit: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onEdit }) => (
  <div>
    <h1>{user.name}</h1>
    <button onClick={onEdit}>Edit</button>
  </div>
);

const UserProfileWithLoading = withLoading(UserProfile);
```

### 2. Render Props Pattern with Type Safety

```typescript
interface DataFetcherProps<T> {
  url: string;
  children: (data: T | null, loading: boolean, error: string | null) => React.ReactNode;
}

function DataFetcher<T>({ url, children }: DataFetcherProps<T>) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    fetch(url)
      .then(response => response.json())
      .then((data: T) => {
        setData(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });
  }, [url]);

  return <>{children(data, loading, error)}</>;
}

// Usage
<DataFetcher<User[]> url="/api/users">
  {(users, loading, error) => {
    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error}</div>;
    return (
      <ul>
        {users?.map(user => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    );
  }}
</DataFetcher>
```

### 3. Custom Hooks with Advanced Types

```typescript
// Generic API hook
function useAPI<T>(url: string) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const refetch = React.useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(url);
      const result: T = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [url]);

  React.useEffect(() => {
    refetch();
  }, [refetch]);

  return { data, loading, error, refetch };
}

// Form hook with validation
type ValidationRule<T> = (value: T) => string | null;

interface UseFormOptions<T> {
  initialValues: T;
  validationRules?: Partial<Record<keyof T, ValidationRule<T[keyof T]>>>;
  onSubmit: (values: T) => void | Promise<void>;
}

function useForm<T extends Record<string, any>>({
  initialValues,
  validationRules = {},
  onSubmit
}: UseFormOptions<T>) {
  const [values, setValues] = React.useState<T>(initialValues);
  const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({});
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const setValue = <K extends keyof T>(field: K, value: T[K]) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    
    Object.keys(validationRules).forEach(field => {
      const rule = validationRules[field as keyof T];
      if (rule) {
        const error = rule(values[field as keyof T]);
        if (error) {
          newErrors[field as keyof T] = error;
        }
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validate()) return;
    
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    values,
    errors,
    isSubmitting,
    setValue,
    handleSubmit,
    validate
  };
}
```

---

*These advanced TypeScript patterns provide the foundation for building type-safe, maintainable, and scalable applications at the senior level.*
