# Advanced Testing Strategies for Senior Developers

This guide covers comprehensive testing strategies, advanced testing patterns, and best practices for senior full-stack developers.

## Testing Pyramid and Strategy

### Testing Levels
```
    /\
   /  \     E2E Tests (Few)
  /____\    - User journey testing
 /      \   - Cross-browser testing
/________\  - Performance testing

/          \  Integration Tests (Some)
/____________\ - API testing
               - Database testing
               - Service integration

/              \  Unit Tests (Many)
/________________\ - Pure functions
                   - Component testing
                   - Business logic
```

### Testing Philosophy
- **Test-Driven Development (TDD)**: Write tests before implementation
- **Behavior-Driven Development (BDD)**: Focus on business behavior
- **Test Coverage**: Aim for 80%+ coverage with meaningful tests
- **Fast Feedback**: Tests should run quickly and provide clear feedback

## Unit Testing Advanced Patterns

### 1. Test Doubles and Mocking Strategies

```typescript
// Mock implementation with Jest
interface UserRepository {
  findById(id: string): Promise<User | null>;
  save(user: User): Promise<User>;
  delete(id: string): Promise<void>;
}

class UserService {
  constructor(private userRepo: UserRepository) {}
  
  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const user = await this.userRepo.findById(id);
    if (!user) {
      throw new Error('User not found');
    }
    
    const updatedUser = { ...user, ...updates };
    return this.userRepo.save(updatedUser);
  }
}

// Test with mocks
describe('UserService', () => {
  let userService: UserService;
  let mockUserRepo: jest.Mocked<UserRepository>;
  
  beforeEach(() => {
    mockUserRepo = {
      findById: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };
    userService = new UserService(mockUserRepo);
  });
  
  describe('updateUser', () => {
    it('should update user successfully', async () => {
      // Arrange
      const userId = '123';
      const existingUser = { id: userId, name: 'John', email: '<EMAIL>' };
      const updates = { name: 'John Doe' };
      const expectedUser = { ...existingUser, ...updates };
      
      mockUserRepo.findById.mockResolvedValue(existingUser);
      mockUserRepo.save.mockResolvedValue(expectedUser);
      
      // Act
      const result = await userService.updateUser(userId, updates);
      
      // Assert
      expect(mockUserRepo.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepo.save).toHaveBeenCalledWith(expectedUser);
      expect(result).toEqual(expectedUser);
    });
    
    it('should throw error when user not found', async () => {
      // Arrange
      const userId = '123';
      mockUserRepo.findById.mockResolvedValue(null);
      
      // Act & Assert
      await expect(userService.updateUser(userId, { name: 'John' }))
        .rejects.toThrow('User not found');
      
      expect(mockUserRepo.save).not.toHaveBeenCalled();
    });
  });
});
```

### 2. Property-Based Testing

```typescript
import fc from 'fast-check';

// Property-based test for sorting function
describe('sort function', () => {
  it('should maintain array length', () => {
    fc.assert(fc.property(
      fc.array(fc.integer()),
      (arr) => {
        const sorted = sort(arr);
        expect(sorted).toHaveLength(arr.length);
      }
    ));
  });
  
  it('should be idempotent', () => {
    fc.assert(fc.property(
      fc.array(fc.integer()),
      (arr) => {
        const sorted1 = sort([...arr]);
        const sorted2 = sort([...sorted1]);
        expect(sorted1).toEqual(sorted2);
      }
    ));
  });
  
  it('should contain all original elements', () => {
    fc.assert(fc.property(
      fc.array(fc.integer()),
      (arr) => {
        const sorted = sort([...arr]);
        const originalSorted = [...arr].sort((a, b) => a - b);
        expect(sorted).toEqual(originalSorted);
      }
    ));
  });
});
```

### 3. Parameterized Tests

```typescript
describe('validation functions', () => {
  describe.each([
    ['valid email', '<EMAIL>', true],
    ['email without @', 'userexample.com', false],
    ['email without domain', 'user@', false],
    ['empty string', '', false],
    ['null value', null, false],
  ])('isValidEmail', (description, input, expected) => {
    it(`should return ${expected} for ${description}`, () => {
      expect(isValidEmail(input)).toBe(expected);
    });
  });
});
```

## Integration Testing

### 1. API Testing with Supertest

```typescript
import request from 'supertest';
import { app } from '../src/app';
import { setupTestDatabase, cleanupTestDatabase } from './helpers/database';

describe('User API', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });
  
  afterAll(async () => {
    await cleanupTestDatabase();
  });
  
  describe('POST /api/users', () => {
    it('should create a new user', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'securePassword123'
      };
      
      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(201);
      
      expect(response.body).toMatchObject({
        id: expect.any(String),
        name: userData.name,
        email: userData.email,
        createdAt: expect.any(String)
      });
      
      expect(response.body.password).toBeUndefined();
    });
    
    it('should return 400 for invalid email', async () => {
      const userData = {
        name: 'John Doe',
        email: 'invalid-email',
        password: 'securePassword123'
      };
      
      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(400);
      
      expect(response.body.errors).toContain('Invalid email format');
    });
  });
});
```

### 2. Database Testing with Testcontainers

```typescript
import { GenericContainer, StartedTestContainer } from 'testcontainers';
import { Client } from 'pg';

describe('Database Integration Tests', () => {
  let container: StartedTestContainer;
  let client: Client;
  
  beforeAll(async () => {
    container = await new GenericContainer('postgres:15')
      .withEnvironment({
        POSTGRES_DB: 'testdb',
        POSTGRES_USER: 'testuser',
        POSTGRES_PASSWORD: 'testpass'
      })
      .withExposedPorts(5432)
      .start();
    
    const port = container.getMappedPort(5432);
    client = new Client({
      host: 'localhost',
      port,
      database: 'testdb',
      user: 'testuser',
      password: 'testpass'
    });
    
    await client.connect();
    
    // Run migrations
    await client.query(`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
  });
  
  afterAll(async () => {
    await client.end();
    await container.stop();
  });
  
  beforeEach(async () => {
    await client.query('TRUNCATE TABLE users RESTART IDENTITY');
  });
  
  it('should insert and retrieve user', async () => {
    // Insert user
    const insertResult = await client.query(
      'INSERT INTO users (name, email) VALUES ($1, $2) RETURNING *',
      ['John Doe', '<EMAIL>']
    );
    
    const insertedUser = insertResult.rows[0];
    expect(insertedUser.name).toBe('John Doe');
    expect(insertedUser.email).toBe('<EMAIL>');
    
    // Retrieve user
    const selectResult = await client.query(
      'SELECT * FROM users WHERE id = $1',
      [insertedUser.id]
    );
    
    expect(selectResult.rows).toHaveLength(1);
    expect(selectResult.rows[0]).toEqual(insertedUser);
  });
});
```

## End-to-End Testing

### 1. Playwright E2E Tests

```typescript
import { test, expect, Page } from '@playwright/test';

test.describe('E-commerce User Journey', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });
  
  test('complete purchase flow', async ({ page }) => {
    // Search for product
    await page.fill('[data-testid="search-input"]', 'laptop');
    await page.click('[data-testid="search-button"]');
    
    // Verify search results
    await expect(page.locator('[data-testid="product-card"]')).toHaveCount(5);
    
    // Select first product
    await page.click('[data-testid="product-card"]:first-child');
    
    // Add to cart
    await page.click('[data-testid="add-to-cart"]');
    await expect(page.locator('[data-testid="cart-count"]')).toHaveText('1');
    
    // Go to cart
    await page.click('[data-testid="cart-icon"]');
    
    // Proceed to checkout
    await page.click('[data-testid="checkout-button"]');
    
    // Fill shipping information
    await page.fill('[data-testid="shipping-name"]', 'John Doe');
    await page.fill('[data-testid="shipping-address"]', '123 Main St');
    await page.fill('[data-testid="shipping-city"]', 'New York');
    await page.selectOption('[data-testid="shipping-state"]', 'NY');
    await page.fill('[data-testid="shipping-zip"]', '10001');
    
    // Fill payment information
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvc"]', '123');
    
    // Complete purchase
    await page.click('[data-testid="place-order"]');
    
    // Verify success
    await expect(page.locator('[data-testid="order-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-number"]')).toContainText('ORDER-');
  });
  
  test('should handle payment failure gracefully', async ({ page }) => {
    // Mock payment failure
    await page.route('**/api/payments', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Payment declined' })
      });
    });
    
    // Go through checkout flow with invalid payment
    await addProductToCart(page);
    await proceedToCheckout(page);
    await fillShippingInfo(page);
    await fillPaymentInfo(page, '****************'); // Declined card
    
    await page.click('[data-testid="place-order"]');
    
    // Verify error handling
    await expect(page.locator('[data-testid="payment-error"]'))
      .toContainText('Payment declined');
    
    // Verify user can retry
    await expect(page.locator('[data-testid="place-order"]')).toBeEnabled();
  });
});

// Helper functions
async function addProductToCart(page: Page) {
  await page.click('[data-testid="product-card"]:first-child');
  await page.click('[data-testid="add-to-cart"]');
}

async function proceedToCheckout(page: Page) {
  await page.click('[data-testid="cart-icon"]');
  await page.click('[data-testid="checkout-button"]');
}

async function fillShippingInfo(page: Page) {
  await page.fill('[data-testid="shipping-name"]', 'John Doe');
  await page.fill('[data-testid="shipping-address"]', '123 Main St');
  await page.fill('[data-testid="shipping-city"]', 'New York');
  await page.selectOption('[data-testid="shipping-state"]', 'NY');
  await page.fill('[data-testid="shipping-zip"]', '10001');
}

async function fillPaymentInfo(page: Page, cardNumber: string = '****************') {
  await page.fill('[data-testid="card-number"]', cardNumber);
  await page.fill('[data-testid="card-expiry"]', '12/25');
  await page.fill('[data-testid="card-cvc"]', '123');
}
```

## Performance Testing

### 1. Load Testing with Artillery

```yaml
# artillery-config.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 300
      arrivalRate: 50
      name: "Sustained load"
    - duration: 120
      arrivalRate: 100
      name: "Peak load"
  payload:
    path: "users.csv"
    fields:
      - "email"
      - "password"

scenarios:
  - name: "User login and browse products"
    weight: 70
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/products"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/api/products/{{ $randomInt(1, 100) }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  - name: "Add product to cart"
    weight: 30
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - post:
          url: "/api/cart/items"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            productId: "{{ $randomInt(1, 100) }}"
            quantity: 1
```

### 2. Frontend Performance Testing

```typescript
// Performance testing with Lighthouse CI
import { test } from '@playwright/test';
import { playAudit } from 'playwright-lighthouse';

test.describe('Performance Tests', () => {
  test('homepage performance audit', async ({ page }) => {
    await page.goto('/');
    
    await playAudit({
      page,
      thresholds: {
        performance: 90,
        accessibility: 95,
        'best-practices': 90,
        seo: 85,
        pwa: 80
      },
      port: 9222
    });
  });
  
  test('product page performance', async ({ page }) => {
    await page.goto('/products/1');
    
    // Measure Core Web Vitals
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const vitals = {};
          
          entries.forEach((entry) => {
            if (entry.name === 'FCP') vitals.fcp = entry.value;
            if (entry.name === 'LCP') vitals.lcp = entry.value;
            if (entry.name === 'FID') vitals.fid = entry.value;
            if (entry.name === 'CLS') vitals.cls = entry.value;
          });
          
          resolve(vitals);
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
      });
    });
    
    // Assert performance metrics
    expect(metrics.lcp).toBeLessThan(2500); // LCP < 2.5s
    expect(metrics.fid).toBeLessThan(100);  // FID < 100ms
    expect(metrics.cls).toBeLessThan(0.1);  // CLS < 0.1
  });
});
```

---

*This comprehensive testing strategy ensures high-quality, reliable, and performant applications through systematic testing at all levels.*
