# Stripe Connect Platform

## 🔗 Marketplace & Multi-Party Payment Platform

Stripe Connect is purpose-built for marketplaces like your healthcare platform. This guide covers everything you need to implement doctor-patient payments with commission handling.

---

## 🎯 Connect Platform Overview

### **What is Stripe Connect?**
Stripe Connect enables platforms to accept payments on behalf of multiple parties, handle commission distribution, and manage complex marketplace scenarios.

### **Perfect for Healthcare Marketplaces**
```
Patient Payment → Platform Commission → Doctor Payout
     $100      →       $2 (2%)      →     $98
```

### **Core Capabilities**
- **Multi-party Payments**: Split payments between platform and providers
- **Commission Handling**: Automatic fee collection and distribution
- **Seller Onboarding**: Streamlined account creation for doctors
- **Compliance Management**: Shared regulatory responsibilities
- **Global Reach**: Support international doctors and patients

---

## 💰 Payment Flow Architecture

### **Healthcare Marketplace Payment Flow**

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Patient   │───▶│   Your Platform │───▶│     Doctor      │
│             │    │                 │    │                 │
│ Pays $100   │    │ Takes $2 fee    │    │ Receives $98    │
│ for consult │    │ (2% commission) │    │ for service     │
└─────────────┘    └─────────────────┘    └─────────────────┘
       │                     │                      │
       ▼                     ▼                      ▼
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Stripe      │    │ Platform        │    │ Doctor Stripe   │
│ Processes   │    │ Account         │    │ Account         │
│ Payment     │    │ (Commission)    │    │ (Service Fee)   │
└─────────────┘    └─────────────────┘    └─────────────────┘
```

### **Technical Implementation**

```javascript
// Create payment with automatic commission split
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000, // $100.00 consultation fee
  currency: 'usd',
  application_fee_amount: 200, // $2.00 platform commission (2%)
  transfer_data: {
    destination: doctorStripeAccount, // Doctor receives $98
  },
  metadata: {
    appointment_id: 'apt_123',
    doctor_id: 'doc_456',
    patient_id: 'pat_789',
    consultation_type: 'video_call'
  }
});
```

---

## 🏗️ Connect Account Management

### **Doctor Account Creation**

```javascript
// Create Express account for new doctor
async function onboardDoctor(doctorData) {
  try {
    const account = await stripe.accounts.create({
      type: 'express',
      country: doctorData.country || 'US',
      email: doctorData.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true }
      },
      business_type: 'individual',
      individual: {
        first_name: doctorData.firstName,
        last_name: doctorData.lastName,
        email: doctorData.email,
        phone: doctorData.phone
      },
      business_profile: {
        mcc: '8011', // Medical practitioners
        product_description: 'Medical consultation services',
        support_email: doctorData.email,
        support_phone: doctorData.phone,
        url: `https://yourplatform.com/doctor/${doctorData.id}`
      },
      metadata: {
        doctor_id: doctorData.id,
        specialty: doctorData.specialty,
        license_number: doctorData.licenseNumber,
        platform: 'healthcare_ai'
      }
    });

    // Save account ID to your database
    await updateDoctorStripeAccount(doctorData.id, account.id);
    
    return account;
  } catch (error) {
    console.error('Error creating doctor account:', error);
    throw error;
  }
}
```

### **Onboarding Link Generation**

```javascript
// Generate onboarding link for doctor verification
async function generateOnboardingLink(doctorId) {
  const doctor = await getDoctorById(doctorId);
  
  if (!doctor.stripe_account_id) {
    throw new Error('Doctor does not have a Stripe account');
  }

  const accountLink = await stripe.accountLinks.create({
    account: doctor.stripe_account_id,
    refresh_url: `${process.env.FRONTEND_URL}/doctor/stripe-refresh`,
    return_url: `${process.env.FRONTEND_URL}/doctor/stripe-success`,
    type: 'account_onboarding'
  });

  return accountLink.url;
}
```

### **Account Status Monitoring**

```javascript
// Check doctor account status and requirements
async function checkDoctorAccountStatus(doctorId) {
  const doctor = await getDoctorById(doctorId);
  
  if (!doctor.stripe_account_id) {
    return { status: 'not_connected' };
  }

  try {
    const account = await stripe.accounts.retrieve(doctor.stripe_account_id);
    
    return {
      status: account.details_submitted ? 'active' : 'pending',
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements,
      verification_status: account.individual?.verification?.status,
      currently_due: account.requirements.currently_due,
      eventually_due: account.requirements.eventually_due,
      past_due: account.requirements.past_due
    };
  } catch (error) {
    console.error('Error checking account status:', error);
    return { status: 'error', error: error.message };
  }
}
```

---

## 💳 Payment Processing

### **Consultation Payment Flow**

```javascript
// Complete payment flow for healthcare consultation
async function processConsultationPayment(appointmentData) {
  const { doctorId, patientId, consultationFee, appointmentTime } = appointmentData;
  
  // 1. Validate doctor account
  const doctor = await getDoctorById(doctorId);
  if (!doctor.stripe_account_id) {
    throw new Error('Doctor payment setup incomplete');
  }
  
  // 2. Check doctor account status
  const accountStatus = await checkDoctorAccountStatus(doctorId);
  if (!accountStatus.charges_enabled) {
    throw new Error('Doctor account not ready to receive payments');
  }
  
  // 3. Calculate fees
  const amount = Math.round(consultationFee * 100); // Convert to cents
  const platformFee = Math.round(amount * 0.02); // 2% commission
  
  // 4. Create payment intent
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount,
    currency: 'usd',
    application_fee_amount: platformFee,
    transfer_data: {
      destination: doctor.stripe_account_id
    },
    metadata: {
      doctor_id: doctorId,
      patient_id: patientId,
      appointment_time: appointmentTime.toISOString(),
      consultation_fee: consultationFee.toString(),
      booking_type: 'consultation'
    }
  });
  
  // 5. Temporarily reserve the time slot
  await reserveTimeSlot(doctorId, appointmentTime, paymentIntent.id);
  
  return {
    client_secret: paymentIntent.client_secret,
    payment_intent_id: paymentIntent.id
  };
}
```

### **Frontend Payment Confirmation**

```javascript
// Frontend: Confirm payment with Stripe
async function confirmConsultationPayment(clientSecret, paymentMethodData) {
  try {
    const { error, paymentIntent } = await stripe.confirmCardPayment(
      clientSecret,
      {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: paymentMethodData.name,
            email: paymentMethodData.email,
            phone: paymentMethodData.phone
          }
        }
      }
    );

    if (error) {
      console.error('Payment failed:', error);
      showError(error.message);
      return { success: false, error };
    }

    if (paymentIntent.status === 'succeeded') {
      console.log('Payment succeeded:', paymentIntent.id);
      showSuccess('Payment successful! Your appointment is being confirmed.');
      
      // Redirect to confirmation page
      window.location.href = `/appointment/confirmation/${paymentIntent.id}`;
      return { success: true, paymentIntent };
    }
  } catch (error) {
    console.error('Payment confirmation error:', error);
    showError('Payment failed. Please try again.');
    return { success: false, error };
  }
}
```

---

## 🔔 Webhook Event Handling

### **Critical Webhook Events**

```javascript
// Essential webhook events for marketplace
const webhookHandler = {
  'payment_intent.succeeded': handleSuccessfulPayment,
  'payment_intent.payment_failed': handleFailedPayment,
  'account.updated': handleAccountUpdate,
  'transfer.created': handleTransferCreated,
  'transfer.paid': handleTransferPaid,
  'payout.created': handlePayoutCreated,
  'invoice.payment_succeeded': handleSubscriptionPayment
};

// Main webhook endpoint
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.log(`Webhook signature verification failed:`, err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  const handler = webhookHandler[event.type];
  if (handler) {
    handler(event.data.object);
  } else {
    console.log(`Unhandled event type: ${event.type}`);
  }

  res.json({ received: true });
});
```

### **Successful Payment Handler**

```javascript
// Handle successful consultation payment
async function handleSuccessfulPayment(paymentIntent) {
  try {
    const { doctor_id, patient_id, appointment_time, consultation_fee } = 
      paymentIntent.metadata;

    // 1. Create confirmed appointment
    const appointment = await createAppointment({
      doctor_id: parseInt(doctor_id),
      patient_id: parseInt(patient_id),
      appointment_time: new Date(appointment_time),
      consultation_fee: parseFloat(consultation_fee),
      payment_intent_id: paymentIntent.id,
      status: 'confirmed',
      payment_status: 'paid'
    });

    // 2. Send confirmation notifications
    await Promise.all([
      sendPatientConfirmation(appointment),
      sendDoctorNotification(appointment),
      sendPlatformNotification(appointment)
    ]);

    // 3. Update doctor's availability
    await markTimeSlotBooked(doctor_id, appointment_time);

    // 4. Log successful transaction
    console.log(`Appointment ${appointment.id} created successfully`);
    
  } catch (error) {
    console.error('Error handling successful payment:', error);
    // Consider implementing retry logic or manual review queue
    await logWebhookError('payment_intent.succeeded', paymentIntent.id, error);
  }
}
```

### **Account Update Handler**

```javascript
// Handle doctor account updates
async function handleAccountUpdate(account) {
  try {
    const doctorId = account.metadata.doctor_id;
    
    if (!doctorId) {
      console.log('Account update for non-doctor account:', account.id);
      return;
    }

    // Update doctor's account status
    await updateDoctorAccountStatus(doctorId, {
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      details_submitted: account.details_submitted,
      requirements: account.requirements
    });

    // Notify doctor if action required
    if (account.requirements.currently_due.length > 0) {
      await notifyDoctorActionRequired(doctorId, account.requirements);
    }

    // Enable doctor profile if account is ready
    if (account.charges_enabled && account.payouts_enabled) {
      await enableDoctorProfile(doctorId);
      await notifyDoctorAccountReady(doctorId);
    }

  } catch (error) {
    console.error('Error handling account update:', error);
    await logWebhookError('account.updated', account.id, error);
  }
}
```

---

## 💸 Payout Management

### **Doctor Earnings Dashboard**

```javascript
// Get doctor's earnings and payout information
async function getDoctorEarnings(doctorId, timeframe = '30d') {
  const doctor = await getDoctorById(doctorId);
  
  if (!doctor.stripe_account_id) {
    return { error: 'Stripe account not connected' };
  }

  try {
    // Get account balance
    const balance = await stripe.balance.retrieve({
      stripeAccount: doctor.stripe_account_id
    });

    // Get recent transfers (earnings from consultations)
    const transfers = await stripe.transfers.list({
      destination: doctor.stripe_account_id,
      limit: 50,
      created: {
        gte: Math.floor((Date.now() - (30 * 24 * 60 * 60 * 1000)) / 1000)
      }
    });

    // Get upcoming payouts
    const payouts = await stripe.payouts.list({
      stripeAccount: doctor.stripe_account_id,
      limit: 10
    });

    // Calculate earnings summary
    const totalEarnings = transfers.data.reduce((sum, transfer) => 
      sum + transfer.amount, 0);
    
    const pendingEarnings = balance.pending.reduce((sum, pending) => 
      sum + pending.amount, 0);
    
    const availableBalance = balance.available.reduce((sum, available) => 
      sum + available.amount, 0);

    return {
      total_earnings: totalEarnings / 100, // Convert from cents
      pending_earnings: pendingEarnings / 100,
      available_balance: availableBalance / 100,
      recent_transfers: transfers.data.map(transfer => ({
        id: transfer.id,
        amount: transfer.amount / 100,
        created: transfer.created,
        description: transfer.description
      })),
      upcoming_payouts: payouts.data.map(payout => ({
        id: payout.id,
        amount: payout.amount / 100,
        arrival_date: payout.arrival_date,
        status: payout.status
      }))
    };

  } catch (error) {
    console.error('Error fetching doctor earnings:', error);
    return { error: 'Unable to fetch earnings data' };
  }
}
```

### **Payout Schedule Management**

```javascript
// Update doctor's payout schedule
async function updatePayoutSchedule(doctorId, schedule) {
  const doctor = await getDoctorById(doctorId);
  
  try {
    const account = await stripe.accounts.update(
      doctor.stripe_account_id,
      {
        settings: {
          payouts: {
            schedule: {
              interval: schedule.interval, // 'daily', 'weekly', 'monthly'
              weekly_anchor: schedule.weekly_anchor, // 'monday', 'tuesday', etc.
              monthly_anchor: schedule.monthly_anchor // 1-31
            }
          }
        }
      }
    );

    return {
      success: true,
      schedule: account.settings.payouts.schedule
    };
  } catch (error) {
    console.error('Error updating payout schedule:', error);
    return { success: false, error: error.message };
  }
}
```

---

## 🌍 International Support

### **Multi-Currency Handling**

```javascript
// Support international doctors and patients
const currencyConfig = {
  'US': { currency: 'usd', symbol: '$' },
  'CA': { currency: 'cad', symbol: 'C$' },
  'GB': { currency: 'gbp', symbol: '£' },
  'EU': { currency: 'eur', symbol: '€' },
  'AU': { currency: 'aud', symbol: 'A$' }
};

async function createInternationalPayment(appointmentData) {
  const { doctorCountry, patientCountry, consultationFee } = appointmentData;
  
  // Determine currency based on doctor's location
  const currency = currencyConfig[doctorCountry]?.currency || 'usd';
  
  // Convert consultation fee to minor units (cents, pence, etc.)
  const amount = convertToMinorUnits(consultationFee, currency);
  const platformFee = Math.round(amount * 0.02);

  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount,
    currency: currency,
    application_fee_amount: platformFee,
    transfer_data: {
      destination: doctorStripeAccount
    },
    metadata: {
      doctor_country: doctorCountry,
      patient_country: patientCountry,
      original_fee: consultationFee.toString(),
      currency: currency
    }
  });

  return paymentIntent;
}
```

---

## 📊 Analytics & Reporting

### **Platform Revenue Analytics**

```javascript
// Get platform commission analytics
async function getPlatformAnalytics(timeframe = '30d') {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(timeframe));

  try {
    // Get all application fees (platform commissions)
    const applicationFees = await stripe.applicationFees.list({
      created: {
        gte: Math.floor(startDate.getTime() / 1000)
      },
      limit: 100
    });

    // Calculate metrics
    const totalCommission = applicationFees.data.reduce((sum, fee) => 
      sum + fee.amount, 0);
    
    const totalTransactions = applicationFees.data.length;
    
    const averageCommission = totalTransactions > 0 ? 
      totalCommission / totalTransactions : 0;

    // Group by doctor for top earners
    const doctorEarnings = {};
    applicationFees.data.forEach(fee => {
      const doctorId = fee.metadata?.doctor_id;
      if (doctorId) {
        doctorEarnings[doctorId] = (doctorEarnings[doctorId] || 0) + fee.amount;
      }
    });

    return {
      total_commission: totalCommission / 100,
      total_transactions: totalTransactions,
      average_commission: averageCommission / 100,
      top_doctors: Object.entries(doctorEarnings)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([doctorId, earnings]) => ({
          doctor_id: doctorId,
          earnings: earnings / 100
        }))
    };

  } catch (error) {
    console.error('Error fetching platform analytics:', error);
    return { error: 'Unable to fetch analytics data' };
  }
}
```

---

**Stripe Connect provides everything you need for your healthcare marketplace. Next, let's explore payment methods and processing options!**

**Next**: [Payment Methods →](./payment-methods.md)
