# System Design for Senior Developers

This section contains advanced system design patterns, scalability techniques, and architectural decisions that senior full-stack developers need to master.

## Core System Design Principles

### 1. Scalability Patterns

#### Horizontal vs Vertical Scaling

- **Vertical Scaling (Scale Up)**: Adding more power to existing machines
- **Horizontal Scaling (Scale Out)**: Adding more machines to the pool of resources

#### Load Balancing Strategies

- **Round Robin**: Requests distributed evenly across servers
- **Least Connections**: Route to server with fewest active connections
- **IP Hash**: Route based on client IP hash
- **Weighted Round Robin**: Assign weights based on server capacity
- **Geographic**: Route based on client location

#### Database Scaling Patterns

- **Read Replicas**: Separate read and write operations
- **Sharding**: Horizontal partitioning of data
- **Federation**: Split databases by function
- **Denormalization**: Trade storage for query performance

### 2. Reliability and Availability Patterns

#### Circuit Breaker Pattern

```typescript
class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime?: Date;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private failureThreshold: number,
    private recoveryTimeout: number
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === "OPEN") {
      if (this.shouldAttemptReset()) {
        this.state = "HALF_OPEN";
      } else {
        throw new Error("Circuit breaker is OPEN");
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private shouldAttemptReset(): boolean {
    return (
      this.lastFailureTime &&
      Date.now() - this.lastFailureTime.getTime() >= this.recoveryTimeout
    );
  }

  private onSuccess(): void {
    this.failureCount = 0;
    this.state = "CLOSED";
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = new Date();

    if (this.failureCount >= this.failureThreshold) {
      this.state = "OPEN";
    }
  }
}
```

#### Bulkhead Pattern

Isolate critical resources to prevent cascade failures:

- **Thread Pool Isolation**: Separate thread pools for different operations
- **Connection Pool Isolation**: Separate database connections
- **Service Isolation**: Separate services for critical vs non-critical operations

#### Retry Patterns

- **Exponential Backoff**: Increase delay between retries exponentially
- **Circuit Breaker Integration**: Stop retrying when circuit is open
- **Jitter**: Add randomness to prevent thundering herd

### 3. Performance Optimization Patterns

#### Caching Strategies

- **Cache-Aside (Lazy Loading)**: Application manages cache
- **Write-Through**: Write to cache and database simultaneously
- **Write-Behind (Write-Back)**: Write to cache first, database later
- **Refresh-Ahead**: Proactively refresh cache before expiration

#### CDN and Edge Computing

- **Static Asset Caching**: Images, CSS, JavaScript files
- **Dynamic Content Caching**: API responses with appropriate TTL
- **Edge Functions**: Serverless functions at edge locations

#### Database Optimization

- **Indexing Strategies**: B-tree, Hash, Bitmap indexes
- **Query Optimization**: Explain plans, query rewriting
- **Connection Pooling**: Manage database connections efficiently
- **Partitioning**: Horizontal and vertical partitioning strategies

## Advanced Architecture Patterns

### 1. Microservices Patterns

#### Service Decomposition

- **Decompose by Business Capability**: Align services with business functions
- **Decompose by Subdomain**: Domain-driven design approach
- **Database per Service**: Each service owns its data

#### Inter-Service Communication

- **Synchronous**: REST, GraphQL, gRPC
- **Asynchronous**: Message queues, event streaming
- **Service Mesh**: Istio, Linkerd for service-to-service communication

#### Data Management Patterns

- **Saga Pattern**: Manage distributed transactions
- **Event Sourcing**: Store events instead of current state
- **CQRS**: Separate read and write models

### 2. Event-Driven Architecture

#### Event Patterns

- **Event Notification**: Simple event publishing
- **Event-Carried State Transfer**: Events contain full state
- **Event Sourcing**: Events as source of truth
- **Event Streaming**: Continuous event processing

#### Message Queue Patterns

- **Publish-Subscribe**: One-to-many message delivery
- **Point-to-Point**: One-to-one message delivery
- **Request-Reply**: Synchronous-like communication over async messaging
- **Dead Letter Queue**: Handle failed message processing

### 3. Security Patterns

#### Authentication and Authorization

- **OAuth 2.0 / OpenID Connect**: Industry standard protocols
- **JWT Tokens**: Stateless authentication
- **API Keys**: Simple authentication for services
- **mTLS**: Mutual TLS for service-to-service authentication

#### Security Best Practices

- **Defense in Depth**: Multiple layers of security
- **Principle of Least Privilege**: Minimal required permissions
- **Zero Trust Architecture**: Never trust, always verify
- **Security by Design**: Build security from the ground up

## System Design Case Studies

### 1. Design a URL Shortener (like bit.ly)

#### Requirements

- **Functional**: Shorten URLs, redirect to original URL, custom aliases
- **Non-Functional**: 100:1 read/write ratio, 100M URLs per day, 99.9% availability

#### High-Level Design

```
[Client] -> [Load Balancer] -> [Web Servers] -> [Cache] -> [Database]
                                            -> [Analytics Service]
```

#### Database Schema

```sql
CREATE TABLE urls (
  id BIGSERIAL PRIMARY KEY,
  short_url VARCHAR(7) UNIQUE NOT NULL,
  long_url TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  user_id BIGINT,
  click_count BIGINT DEFAULT 0
);

CREATE INDEX idx_short_url ON urls(short_url);
CREATE INDEX idx_user_id ON urls(user_id);
```

#### Key Design Decisions

- **Base62 Encoding**: For short URL generation
- **Database Sharding**: Shard by short_url hash
- **Caching**: Redis for frequently accessed URLs
- **Analytics**: Separate service for click tracking

### 2. Design a Chat System

#### Requirements

- **Functional**: 1-on-1 chat, group chat, online presence, message history
- **Non-Functional**: Real-time messaging, 50M DAU, 99.99% availability

#### High-Level Design

```
[Mobile/Web] -> [Load Balancer] -> [Chat Service] -> [Message Queue]
                                -> [Presence Service] -> [Database]
                                -> [Notification Service]
```

#### Technology Choices

- **WebSocket**: Real-time bidirectional communication
- **Message Queue**: Apache Kafka for message ordering
- **Database**: Cassandra for message storage (time-series data)
- **Cache**: Redis for online presence and recent messages

## Performance Metrics and Monitoring

### Key Metrics to Track

- **Latency**: Response time percentiles (P50, P95, P99)
- **Throughput**: Requests per second, transactions per second
- **Error Rate**: 4xx and 5xx error percentages
- **Availability**: Uptime percentage
- **Resource Utilization**: CPU, memory, disk, network

### Monitoring Tools

- **Application Performance Monitoring**: New Relic, DataDog, Dynatrace
- **Infrastructure Monitoring**: Prometheus + Grafana
- **Log Aggregation**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Distributed Tracing**: Jaeger, Zipkin
- **Synthetic Monitoring**: Pingdom, Uptime Robot

### Alerting Best Practices

- **SLI/SLO Based Alerting**: Alert on service level objectives
- **Runbook Automation**: Automated response to common issues
- **Alert Fatigue Prevention**: Proper alert thresholds and grouping
- **Escalation Policies**: Clear escalation paths for different severity levels

## Problem Solving Methodologies

Senior developers distinguish themselves not just by their technical knowledge, but by their systematic approach to solving complex problems. This section covers structured methodologies for tackling technical challenges at scale.

### 1. Problem Analysis Framework

#### The DEFINE Method

A systematic approach to breaking down complex problems:

**D** - **Define** the problem clearly and specifically
**E** - **Explore** the problem space and gather context
**F** - **Frame** the problem in terms of constraints and requirements
**I** - **Identify** stakeholders and success criteria
**N** - **Narrow** down to the core issues
**E** - **Evaluate** potential approaches

#### Problem Decomposition Techniques

```typescript
interface ProblemAnalysis {
  problem: string;
  symptoms: string[];
  constraints: {
    technical: string[];
    business: string[];
    timeline: string;
    resources: string[];
  };
  stakeholders: {
    primary: string[];
    secondary: string[];
  };
  successCriteria: {
    functional: string[];
    nonFunctional: string[];
    business: string[];
  };
}

class ProblemAnalyzer {
  static analyze(description: string): ProblemAnalysis {
    return {
      problem: this.extractCoreProblem(description),
      symptoms: this.identifySymptoms(description),
      constraints: this.mapConstraints(description),
      stakeholders: this.identifyStakeholders(description),
      successCriteria: this.defineSuccessCriteria(description),
    };
  }

  static decompose(problem: ProblemAnalysis): SubProblem[] {
    // Break down into manageable components
    return [
      {
        id: "data-layer",
        description: "Data storage and retrieval challenges",
        priority: "high",
        dependencies: [],
      },
      {
        id: "api-layer",
        description: "Service interface and communication",
        priority: "high",
        dependencies: ["data-layer"],
      },
      {
        id: "ui-layer",
        description: "User interface and experience",
        priority: "medium",
        dependencies: ["api-layer"],
      },
    ];
  }
}
```

#### Context Mapping

Understanding the broader system context:

```mermaid
graph TD
    A[Problem Context] --> B[Technical Context]
    A --> C[Business Context]
    A --> D[User Context]
    A --> E[Organizational Context]

    B --> B1[Current Architecture]
    B --> B2[Technology Stack]
    B --> B3[Performance Requirements]
    B --> B4[Security Constraints]

    C --> C1[Business Goals]
    C --> C2[Budget Constraints]
    C --> C3[Timeline Requirements]
    C --> C4[Compliance Needs]

    D --> D1[User Needs]
    D --> D2[Usage Patterns]
    D --> D3[Accessibility Requirements]

    E --> E1[Team Structure]
    E --> E2[Skill Sets]
    E --> E3[Communication Patterns]
```

### 2. Root Cause Analysis Techniques

#### The 5 Whys Method

Iteratively asking "why" to drill down to root causes:

```typescript
interface WhyAnalysis {
  problem: string;
  whys: {
    question: string;
    answer: string;
    evidence: string[];
  }[];
  rootCause: string;
  actionItems: string[];
}

class FiveWhysAnalyzer {
  static analyze(initialProblem: string): WhyAnalysis {
    const analysis: WhyAnalysis = {
      problem: initialProblem,
      whys: [],
      rootCause: "",
      actionItems: [],
    };

    // Example: API response time is slow
    analysis.whys = [
      {
        question: "Why is the API response time slow?",
        answer: "Database queries are taking too long",
        evidence: ["Query execution time > 2s", "Database CPU at 90%"],
      },
      {
        question: "Why are database queries taking too long?",
        answer: "Missing indexes on frequently queried columns",
        evidence: ["EXPLAIN shows full table scans", "Query plan analysis"],
      },
      {
        question: "Why are indexes missing?",
        answer: "Database schema wasn't optimized for current query patterns",
        evidence: ["Schema designed 2 years ago", "Query patterns evolved"],
      },
      {
        question: "Why wasn't the schema updated?",
        answer: "No monitoring of query performance over time",
        evidence: [
          "No query performance metrics",
          "No alerting on slow queries",
        ],
      },
      {
        question: "Why is there no query performance monitoring?",
        answer: "Monitoring strategy focused only on application metrics",
        evidence: [
          "APM tools don't cover database layer",
          "No DBA involvement",
        ],
      },
    ];

    analysis.rootCause =
      "Insufficient database performance monitoring and optimization processes";
    analysis.actionItems = [
      "Implement database performance monitoring",
      "Add missing indexes based on current query patterns",
      "Establish regular database performance review process",
      "Include database metrics in alerting strategy",
    ];

    return analysis;
  }
}
```

#### Fishbone (Ishikawa) Diagram

Systematic categorization of potential causes:

```typescript
interface FishboneAnalysis {
  problem: string;
  categories: {
    [category: string]: {
      causes: string[];
      subCauses: { [cause: string]: string[] };
    };
  };
}

class FishboneAnalyzer {
  static analyze(problem: string): FishboneAnalysis {
    return {
      problem: "High API Latency",
      categories: {
        People: {
          causes: [
            "Lack of expertise",
            "Insufficient training",
            "Team communication",
          ],
          subCauses: {
            "Lack of expertise": [
              "No database optimization skills",
              "Limited monitoring experience",
            ],
            "Team communication": [
              "Poor handoff between teams",
              "Unclear responsibilities",
            ],
          },
        },
        Process: {
          causes: [
            "No performance testing",
            "Inadequate code review",
            "Missing monitoring",
          ],
          subCauses: {
            "No performance testing": [
              "No load testing in CI",
              "No baseline metrics",
            ],
            "Missing monitoring": [
              "No SLI/SLO definition",
              "Reactive vs proactive monitoring",
            ],
          },
        },
        Technology: {
          causes: ["Database design", "Caching strategy", "Network latency"],
          subCauses: {
            "Database design": [
              "Missing indexes",
              "Inefficient queries",
              "No connection pooling",
            ],
            "Caching strategy": [
              "No caching layer",
              "Cache invalidation issues",
            ],
          },
        },
        Environment: {
          causes: [
            "Infrastructure limits",
            "Third-party dependencies",
            "Network issues",
          ],
          subCauses: {
            "Infrastructure limits": [
              "CPU constraints",
              "Memory limitations",
              "Disk I/O bottlenecks",
            ],
          },
        },
      },
    };
  }
}
```

#### Fault Tree Analysis

Top-down approach for complex system failures:

```typescript
interface FaultTreeNode {
  id: string;
  description: string;
  type: "AND" | "OR" | "BASIC_EVENT";
  probability?: number;
  children?: FaultTreeNode[];
  mitigations?: string[];
}

class FaultTreeAnalyzer {
  static buildTree(topEvent: string): FaultTreeNode {
    return {
      id: "top",
      description: "System Unavailable",
      type: "OR",
      children: [
        {
          id: "frontend-down",
          description: "Frontend Service Down",
          type: "OR",
          children: [
            {
              id: "frontend-crash",
              description: "Application Crash",
              type: "BASIC_EVENT",
              probability: 0.01,
              mitigations: [
                "Health checks",
                "Auto-restart",
                "Circuit breakers",
              ],
            },
            {
              id: "frontend-deploy-fail",
              description: "Deployment Failure",
              type: "BASIC_EVENT",
              probability: 0.005,
              mitigations: ["Blue-green deployment", "Rollback automation"],
            },
          ],
        },
        {
          id: "backend-down",
          description: "Backend Service Down",
          type: "AND",
          children: [
            {
              id: "db-unavailable",
              description: "Database Unavailable",
              type: "OR",
              children: [
                {
                  id: "db-crash",
                  description: "Database Crash",
                  type: "BASIC_EVENT",
                  probability: 0.001,
                  mitigations: ["Database clustering", "Automated failover"],
                },
                {
                  id: "db-connection-pool-exhausted",
                  description: "Connection Pool Exhausted",
                  type: "BASIC_EVENT",
                  probability: 0.02,
                  mitigations: [
                    "Connection pool monitoring",
                    "Dynamic scaling",
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  }

  static calculateSystemReliability(tree: FaultTreeNode): number {
    // Calculate overall system reliability based on fault tree
    if (tree.type === "BASIC_EVENT") {
      return 1 - (tree.probability || 0);
    }

    if (tree.type === "OR") {
      // For OR gates, system fails if any child fails
      let reliability = 1;
      tree.children?.forEach((child) => {
        reliability *= this.calculateSystemReliability(child);
      });
      return reliability;
    }

    if (tree.type === "AND") {
      // For AND gates, system fails only if all children fail
      let failureProbability = 1;
      tree.children?.forEach((child) => {
        failureProbability *= 1 - this.calculateSystemReliability(child);
      });
      return 1 - failureProbability;
    }

    return 1;
  }
}
```

### 3. Trade-off Analysis

#### CAP Theorem Decision Framework

Systematic approach to distributed system trade-offs:

```typescript
interface CAPAnalysis {
  consistency: {
    requirement: "strong" | "eventual" | "weak";
    rationale: string;
    implications: string[];
  };
  availability: {
    requirement: number; // SLA percentage
    rationale: string;
    implications: string[];
  };
  partitionTolerance: {
    requirement: "required" | "optional";
    rationale: string;
    implications: string[];
  };
  recommendation: "CP" | "AP" | "CA";
  tradeoffs: string[];
}

class CAPAnalyzer {
  static analyze(systemRequirements: any): CAPAnalysis {
    // Example: E-commerce checkout system
    return {
      consistency: {
        requirement: "strong",
        rationale: "Financial transactions require ACID properties",
        implications: [
          "May need to sacrifice availability during network partitions",
          "Requires distributed locking mechanisms",
          "Higher latency for write operations",
        ],
      },
      availability: {
        requirement: 99.9,
        rationale: "Business critical system with revenue impact",
        implications: [
          "Need redundancy and failover mechanisms",
          "May require eventual consistency for some operations",
          "Investment in monitoring and alerting",
        ],
      },
      partitionTolerance: {
        requirement: "required",
        rationale: "Distributed system across multiple data centers",
        implications: [
          "Must handle network failures gracefully",
          "Need consensus algorithms for coordination",
          "Complexity in conflict resolution",
        ],
      },
      recommendation: "CP",
      tradeoffs: [
        "Prioritize consistency over availability during partitions",
        "Implement graceful degradation for non-critical features",
        "Use eventual consistency for analytics and reporting",
      ],
    };
  }
}
```

#### Performance vs. Consistency Trade-offs

```typescript
interface PerformanceConsistencyAnalysis {
  scenario: string;
  performanceRequirements: {
    latency: string;
    throughput: string;
    scalability: string;
  };
  consistencyRequirements: {
    level: "strong" | "eventual" | "weak";
    tolerance: string;
    businessImpact: string;
  };
  recommendation: {
    approach: string;
    implementation: string[];
    monitoring: string[];
  };
}

class PerformanceConsistencyAnalyzer {
  static analyze(scenario: string): PerformanceConsistencyAnalysis {
    // Example: Social media feed
    return {
      scenario: "Social Media Feed",
      performanceRequirements: {
        latency: "< 200ms for feed loading",
        throughput: "10,000 requests/second",
        scalability: "Handle 1M+ concurrent users",
      },
      consistencyRequirements: {
        level: "eventual",
        tolerance: "Users can see slightly stale content",
        businessImpact: "Low - eventual consistency acceptable for feeds",
      },
      recommendation: {
        approach: "Prioritize performance with eventual consistency",
        implementation: [
          "Use read replicas for feed generation",
          "Implement write-behind caching",
          "Use event-driven updates for real-time features",
          "Cache user feeds with TTL-based invalidation",
        ],
        monitoring: [
          "Track replication lag",
          "Monitor cache hit rates",
          "Measure feed generation time",
          "Alert on consistency violations",
        ],
      },
    };
  }
}
```

#### Cost vs. Scalability Analysis

```typescript
interface CostScalabilityMatrix {
  solutions: {
    name: string;
    initialCost: number;
    scalingCost: number;
    operationalComplexity: "low" | "medium" | "high";
    scalabilityLimit: string;
    pros: string[];
    cons: string[];
  }[];
  recommendation: string;
  rationale: string;
}

class CostScalabilityAnalyzer {
  static analyze(requirements: any): CostScalabilityMatrix {
    return {
      solutions: [
        {
          name: "Monolithic Architecture",
          initialCost: 10000,
          scalingCost: 50000,
          operationalComplexity: "low",
          scalabilityLimit: "Limited by single database",
          pros: [
            "Simple deployment",
            "Easy debugging",
            "Lower initial complexity",
          ],
          cons: [
            "Scaling bottlenecks",
            "Technology lock-in",
            "Single point of failure",
          ],
        },
        {
          name: "Microservices Architecture",
          initialCost: 50000,
          scalingCost: 20000,
          operationalComplexity: "high",
          scalabilityLimit: "Highly scalable with proper design",
          pros: [
            "Independent scaling",
            "Technology diversity",
            "Fault isolation",
          ],
          cons: [
            "Higher complexity",
            "Network overhead",
            "Distributed system challenges",
          ],
        },
        {
          name: "Serverless Architecture",
          initialCost: 5000,
          scalingCost: 15000,
          operationalComplexity: "medium",
          scalabilityLimit: "Auto-scaling with cloud provider limits",
          pros: ["Pay-per-use", "Auto-scaling", "Reduced operational overhead"],
          cons: [
            "Vendor lock-in",
            "Cold start latency",
            "Limited execution time",
          ],
        },
      ],
      recommendation:
        "Hybrid approach: Start with modular monolith, extract services as needed",
      rationale:
        "Balances initial development speed with future scalability options",
    };
  }
}
```

### 4. Decision Making Frameworks

#### DACI Framework (Driver, Approver, Contributors, Informed)

```typescript
interface DACIDecision {
  decision: string;
  driver: string;
  approver: string;
  contributors: string[];
  informed: string[];
  timeline: {
    start: Date;
    decision: Date;
    implementation: Date;
  };
  criteria: {
    name: string;
    weight: number;
    description: string;
  }[];
  options: {
    name: string;
    scores: { [criterion: string]: number };
    pros: string[];
    cons: string[];
  }[];
}

class DACIFramework {
  static createDecision(decisionTitle: string): DACIDecision {
    return {
      decision: "Choose Database Technology for New Microservice",
      driver: "Senior Backend Engineer",
      approver: "Engineering Manager",
      contributors: [
        "Database Administrator",
        "DevOps Engineer",
        "Product Manager",
      ],
      informed: ["Frontend Team", "QA Team", "Security Team"],
      timeline: {
        start: new Date("2024-01-01"),
        decision: new Date("2024-01-15"),
        implementation: new Date("2024-02-01"),
      },
      criteria: [
        {
          name: "Performance",
          weight: 0.3,
          description: "Query performance and throughput",
        },
        {
          name: "Scalability",
          weight: 0.25,
          description: "Ability to handle growth",
        },
        {
          name: "Operational Complexity",
          weight: 0.2,
          description: "Ease of maintenance",
        },
        { name: "Cost", weight: 0.15, description: "Total cost of ownership" },
        {
          name: "Team Expertise",
          weight: 0.1,
          description: "Current team knowledge",
        },
      ],
      options: [
        {
          name: "PostgreSQL",
          scores: {
            Performance: 8,
            Scalability: 7,
            "Operational Complexity": 9,
            Cost: 9,
            "Team Expertise": 9,
          },
          pros: ["ACID compliance", "Rich feature set", "Strong community"],
          cons: ["Vertical scaling limitations", "Complex replication setup"],
        },
        {
          name: "MongoDB",
          scores: {
            Performance: 7,
            Scalability: 9,
            "Operational Complexity": 7,
            Cost: 8,
            "Team Expertise": 6,
          },
          pros: ["Horizontal scaling", "Flexible schema", "Built-in sharding"],
          cons: ["Eventual consistency", "Memory usage", "Learning curve"],
        },
      ],
    };
  }

  static calculateScores(decision: DACIDecision): { [option: string]: number } {
    const scores: { [option: string]: number } = {};

    decision.options.forEach((option) => {
      let totalScore = 0;
      decision.criteria.forEach((criterion) => {
        totalScore += (option.scores[criterion.name] || 0) * criterion.weight;
      });
      scores[option.name] = totalScore;
    });

    return scores;
  }
}
```

#### Architectural Decision Records (ADRs)

```typescript
interface ADR {
  id: string;
  title: string;
  status: "proposed" | "accepted" | "deprecated" | "superseded";
  date: Date;
  context: string;
  decision: string;
  rationale: string;
  consequences: {
    positive: string[];
    negative: string[];
    risks: string[];
  };
  alternatives: {
    name: string;
    description: string;
    rejectionReason: string;
  }[];
  implementation: {
    steps: string[];
    timeline: string;
    resources: string[];
  };
  reviewDate?: Date;
  supersededBy?: string;
}

class ADRManager {
  static createADR(title: string): ADR {
    return {
      id: "ADR-001",
      title: "Use Event Sourcing for Order Management",
      status: "accepted",
      date: new Date("2024-01-15"),
      context: `
        Our e-commerce platform needs to handle complex order workflows with multiple state changes.
        Current approach using CRUD operations makes it difficult to:
        - Track order history and audit trails
        - Handle concurrent updates safely
        - Implement complex business rules
        - Provide real-time analytics
      `,
      decision: "Implement Event Sourcing pattern for order management service",
      rationale: `
        Event Sourcing provides:
        1. Complete audit trail of all order changes
        2. Natural handling of concurrent updates
        3. Ability to replay events for debugging
        4. Foundation for real-time analytics
        5. Support for complex business workflows
      `,
      consequences: {
        positive: [
          "Complete audit trail for compliance",
          "Better handling of concurrent operations",
          "Simplified debugging and troubleshooting",
          "Foundation for real-time analytics",
          "Support for complex business rules",
        ],
        negative: [
          "Increased system complexity",
          "Learning curve for team",
          "Additional infrastructure requirements",
          "Eventual consistency challenges",
        ],
        risks: [
          "Event schema evolution complexity",
          "Increased storage requirements",
          "Potential performance impact on reads",
        ],
      },
      alternatives: [
        {
          name: "Traditional CRUD with Audit Log",
          description:
            "Keep current approach but add comprehensive audit logging",
          rejectionReason:
            "Doesn't solve concurrent update issues and adds complexity without benefits",
        },
        {
          name: "State Machine Pattern",
          description: "Implement explicit state machine for order workflows",
          rejectionReason:
            "Doesn't provide audit trail and history tracking capabilities",
        },
      ],
      implementation: {
        steps: [
          "Design event schema and aggregate structure",
          "Implement event store using PostgreSQL",
          "Create event handlers for order projections",
          "Implement command handlers for order operations",
          "Add event versioning and migration strategy",
          "Create monitoring and alerting for event processing",
        ],
        timeline: "6 weeks",
        resources: [
          "2 Senior Engineers",
          "1 DevOps Engineer",
          "Database Administrator",
        ],
      },
      reviewDate: new Date("2024-07-15"),
    };
  }

  static reviewADR(adr: ADR): {
    shouldUpdate: boolean;
    recommendations: string[];
  } {
    const monthsSinceDecision =
      (Date.now() - adr.date.getTime()) / (1000 * 60 * 60 * 24 * 30);

    return {
      shouldUpdate: monthsSinceDecision > 6,
      recommendations: [
        "Review implementation outcomes against expected consequences",
        "Assess if assumptions in context still hold true",
        "Evaluate if better alternatives have emerged",
        "Update status if decision has been superseded",
      ],
    };
  }
}
```

### 5. Debugging Distributed Systems

#### Distributed Tracing Strategy

```typescript
interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  baggage: { [key: string]: string };
  flags: number;
}

interface Span {
  traceId: string;
  spanId: string;
  operationName: string;
  startTime: number;
  duration?: number;
  tags: { [key: string]: any };
  logs: { timestamp: number; fields: { [key: string]: any } }[];
  references: { type: "child_of" | "follows_from"; spanId: string }[];
}

class DistributedTracer {
  static createTrace(operationName: string): TraceContext {
    return {
      traceId: this.generateId(),
      spanId: this.generateId(),
      baggage: {},
      flags: 1,
    };
  }

  static createChildSpan(
    parent: TraceContext,
    operationName: string
  ): TraceContext {
    return {
      traceId: parent.traceId,
      spanId: this.generateId(),
      parentSpanId: parent.spanId,
      baggage: { ...parent.baggage },
      flags: parent.flags,
    };
  }

  static addBaggage(context: TraceContext, key: string, value: string): void {
    context.baggage[key] = value;
  }

  static instrumentHTTPRequest(context: TraceContext): {
    [header: string]: string;
  } {
    return {
      "X-Trace-ID": context.traceId,
      "X-Span-ID": context.spanId,
      "X-Parent-Span-ID": context.parentSpanId || "",
      "X-Baggage": JSON.stringify(context.baggage),
    };
  }

  static extractFromHTTPHeaders(headers: {
    [key: string]: string;
  }): TraceContext | null {
    const traceId = headers["x-trace-id"];
    const spanId = headers["x-span-id"];

    if (!traceId || !spanId) return null;

    return {
      traceId,
      spanId,
      parentSpanId: headers["x-parent-span-id"] || undefined,
      baggage: JSON.parse(headers["x-baggage"] || "{}"),
      flags: 1,
    };
  }

  private static generateId(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }
}
```

#### Correlation ID Pattern

```typescript
interface CorrelationContext {
  correlationId: string;
  userId?: string;
  sessionId?: string;
  requestId: string;
  timestamp: Date;
  source: string;
}

class CorrelationManager {
  private static context: CorrelationContext | null = null;

  static initializeContext(
    source: string,
    userId?: string
  ): CorrelationContext {
    this.context = {
      correlationId: this.generateCorrelationId(),
      userId,
      sessionId: this.generateSessionId(),
      requestId: this.generateRequestId(),
      timestamp: new Date(),
      source,
    };
    return this.context;
  }

  static getCurrentContext(): CorrelationContext | null {
    return this.context;
  }

  static propagateToHeaders(): { [key: string]: string } {
    if (!this.context) return {};

    return {
      "X-Correlation-ID": this.context.correlationId,
      "X-User-ID": this.context.userId || "",
      "X-Session-ID": this.context.sessionId || "",
      "X-Request-ID": this.context.requestId,
      "X-Source": this.context.source,
    };
  }

  static extractFromHeaders(headers: {
    [key: string]: string;
  }): CorrelationContext | null {
    const correlationId = headers["x-correlation-id"];
    if (!correlationId) return null;

    return {
      correlationId,
      userId: headers["x-user-id"] || undefined,
      sessionId: headers["x-session-id"] || undefined,
      requestId: headers["x-request-id"] || this.generateRequestId(),
      timestamp: new Date(),
      source: headers["x-source"] || "unknown",
    };
  }

  static logWithContext(level: string, message: string, metadata?: any): void {
    const context = this.getCurrentContext();
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      correlationId: context?.correlationId,
      userId: context?.userId,
      sessionId: context?.sessionId,
      requestId: context?.requestId,
      source: context?.source,
      ...metadata,
    };

    console.log(JSON.stringify(logEntry));
  }

  private static generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  private static generateSessionId(): string {
    return `sess_${Math.random().toString(36).substring(2, 15)}`;
  }

  private static generateRequestId(): string {
    return `req_${Math.random().toString(36).substring(2, 10)}`;
  }
}
```

#### Circuit Breaker with Observability

```typescript
interface CircuitBreakerMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  timeouts: number;
  circuitOpenTime?: Date;
  lastFailureTime?: Date;
  averageResponseTime: number;
}

class ObservableCircuitBreaker {
  private metrics: CircuitBreakerMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    timeouts: 0,
    averageResponseTime: 0,
  };

  private responseTimes: number[] = [];
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private failureThreshold: number,
    private recoveryTimeout: number,
    private timeoutThreshold: number,
    private serviceName: string
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    const context = CorrelationManager.getCurrentContext();

    this.metrics.totalRequests++;

    try {
      if (this.state === "OPEN") {
        if (this.shouldAttemptReset()) {
          this.state = "HALF_OPEN";
          CorrelationManager.logWithContext(
            "info",
            `Circuit breaker transitioning to HALF_OPEN for ${this.serviceName}`,
            { circuitBreakerState: "HALF_OPEN", metrics: this.metrics }
          );
        } else {
          throw new Error(`Circuit breaker is OPEN for ${this.serviceName}`);
        }
      }

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(
          () => reject(new Error("Operation timeout")),
          this.timeoutThreshold
        );
      });

      const result = await Promise.race([operation(), timeoutPromise]);

      const responseTime = Date.now() - startTime;
      this.recordSuccess(responseTime);

      CorrelationManager.logWithContext(
        "debug",
        `Successful operation for ${this.serviceName}`,
        { responseTime, circuitBreakerState: this.state }
      );

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;

      if (error.message === "Operation timeout") {
        this.recordTimeout(responseTime);
        CorrelationManager.logWithContext(
          "warn",
          `Timeout for ${this.serviceName}`,
          {
            responseTime,
            error: error.message,
            circuitBreakerState: this.state,
          }
        );
      } else {
        this.recordFailure(responseTime);
        CorrelationManager.logWithContext(
          "error",
          `Failure for ${this.serviceName}`,
          {
            responseTime,
            error: error.message,
            circuitBreakerState: this.state,
          }
        );
      }

      throw error;
    }
  }

  private recordSuccess(responseTime: number): void {
    this.metrics.successfulRequests++;
    this.updateResponseTime(responseTime);

    if (this.state === "HALF_OPEN") {
      this.state = "CLOSED";
      this.metrics.circuitOpenTime = undefined;
      CorrelationManager.logWithContext(
        "info",
        `Circuit breaker CLOSED for ${this.serviceName}`,
        { circuitBreakerState: "CLOSED", metrics: this.metrics }
      );
    }
  }

  private recordFailure(responseTime: number): void {
    this.metrics.failedRequests++;
    this.metrics.lastFailureTime = new Date();
    this.updateResponseTime(responseTime);

    if (this.shouldOpenCircuit()) {
      this.state = "OPEN";
      this.metrics.circuitOpenTime = new Date();
      CorrelationManager.logWithContext(
        "error",
        `Circuit breaker OPENED for ${this.serviceName}`,
        { circuitBreakerState: "OPEN", metrics: this.metrics }
      );
    }
  }

  private recordTimeout(responseTime: number): void {
    this.metrics.timeouts++;
    this.recordFailure(responseTime);
  }

  private updateResponseTime(responseTime: number): void {
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift();
    }

    this.metrics.averageResponseTime =
      this.responseTimes.reduce((sum, time) => sum + time, 0) /
      this.responseTimes.length;
  }

  private shouldOpenCircuit(): boolean {
    const failureRate =
      this.metrics.failedRequests / this.metrics.totalRequests;
    return failureRate >= this.failureThreshold;
  }

  private shouldAttemptReset(): boolean {
    return (
      this.metrics.circuitOpenTime &&
      Date.now() - this.metrics.circuitOpenTime.getTime() >=
        this.recoveryTimeout
    );
  }

  getMetrics(): CircuitBreakerMetrics {
    return { ...this.metrics };
  }
}
```

### 6. Performance Problem Solving

#### Performance Investigation Framework

```typescript
interface PerformanceProfile {
  timestamp: Date;
  endpoint: string;
  method: string;
  responseTime: number;
  cpuUsage: number;
  memoryUsage: number;
  dbQueryTime: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
}

interface PerformanceBottleneck {
  type:
    | "cpu"
    | "memory"
    | "database"
    | "network"
    | "cache"
    | "external_service";
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  metrics: { [key: string]: number };
  recommendations: string[];
  estimatedImpact: string;
}

class PerformanceAnalyzer {
  static analyzeProfile(profile: PerformanceProfile): PerformanceBottleneck[] {
    const bottlenecks: PerformanceBottleneck[] = [];

    // Database performance analysis
    if (profile.dbQueryTime > 1000) {
      bottlenecks.push({
        type: "database",
        severity: profile.dbQueryTime > 5000 ? "critical" : "high",
        description: "Slow database queries detected",
        metrics: {
          queryTime: profile.dbQueryTime,
          threshold: 1000,
        },
        recommendations: [
          "Analyze slow query log",
          "Check for missing indexes",
          "Consider query optimization",
          "Implement connection pooling",
          "Add database monitoring",
        ],
        estimatedImpact:
          "Reducing query time by 50% could improve response time by 30%",
      });
    }

    // Memory usage analysis
    if (profile.memoryUsage > 0.8) {
      bottlenecks.push({
        type: "memory",
        severity: profile.memoryUsage > 0.95 ? "critical" : "high",
        description: "High memory usage detected",
        metrics: {
          memoryUsage: profile.memoryUsage,
          threshold: 0.8,
        },
        recommendations: [
          "Analyze memory leaks",
          "Implement object pooling",
          "Optimize data structures",
          "Add memory profiling",
          "Consider horizontal scaling",
        ],
        estimatedImpact:
          "Memory optimization could prevent GC pauses and improve stability",
      });
    }

    // Cache performance analysis
    if (profile.cacheHitRate < 0.8) {
      bottlenecks.push({
        type: "cache",
        severity: profile.cacheHitRate < 0.5 ? "high" : "medium",
        description: "Low cache hit rate detected",
        metrics: {
          hitRate: profile.cacheHitRate,
          threshold: 0.8,
        },
        recommendations: [
          "Review cache key strategy",
          "Optimize cache TTL settings",
          "Implement cache warming",
          "Add cache monitoring",
          "Consider cache partitioning",
        ],
        estimatedImpact:
          "Improving cache hit rate to 90% could reduce response time by 40%",
      });
    }

    // CPU usage analysis
    if (profile.cpuUsage > 0.8) {
      bottlenecks.push({
        type: "cpu",
        severity: profile.cpuUsage > 0.95 ? "critical" : "high",
        description: "High CPU usage detected",
        metrics: {
          cpuUsage: profile.cpuUsage,
          threshold: 0.8,
        },
        recommendations: [
          "Profile CPU-intensive operations",
          "Optimize algorithms",
          "Implement async processing",
          "Add CPU monitoring",
          "Consider load balancing",
        ],
        estimatedImpact: "CPU optimization could improve throughput by 25%",
      });
    }

    return bottlenecks;
  }

  static generateOptimizationPlan(bottlenecks: PerformanceBottleneck[]): {
    priority: PerformanceBottleneck[];
    quickWins: PerformanceBottleneck[];
    longTerm: PerformanceBottleneck[];
  } {
    const critical = bottlenecks.filter((b) => b.severity === "critical");
    const high = bottlenecks.filter((b) => b.severity === "high");
    const medium = bottlenecks.filter((b) => b.severity === "medium");

    return {
      priority: [...critical, ...high],
      quickWins: bottlenecks.filter(
        (b) =>
          b.type === "cache" ||
          (b.type === "database" && b.metrics.queryTime < 2000)
      ),
      longTerm: bottlenecks.filter(
        (b) =>
          b.type === "cpu" ||
          b.type === "memory" ||
          (b.type === "database" && b.metrics.queryTime > 5000)
      ),
    };
  }
}
```

### 7. Incident Response and Post-Mortems

#### Incident Response Framework

```typescript
interface Incident {
  id: string;
  title: string;
  severity: "P0" | "P1" | "P2" | "P3" | "P4";
  status: "investigating" | "identified" | "monitoring" | "resolved";
  startTime: Date;
  endTime?: Date;
  description: string;
  impact: {
    usersAffected: number;
    servicesAffected: string[];
    businessImpact: string;
    revenueImpact?: number;
  };
  timeline: IncidentEvent[];
  responders: {
    incidentCommander: string;
    technicalLead: string;
    communicationsLead: string;
    stakeholders: string[];
  };
  rootCause?: string;
  resolution?: string;
  actionItems: ActionItem[];
}

interface IncidentEvent {
  timestamp: Date;
  type:
    | "detection"
    | "escalation"
    | "investigation"
    | "mitigation"
    | "resolution"
    | "communication";
  description: string;
  author: string;
  metadata?: { [key: string]: any };
}

interface ActionItem {
  id: string;
  description: string;
  assignee: string;
  priority: "high" | "medium" | "low";
  dueDate: Date;
  status: "open" | "in_progress" | "completed";
  category: "prevention" | "detection" | "response" | "recovery";
}

class IncidentManager {
  static createIncident(
    title: string,
    severity: Incident["severity"]
  ): Incident {
    const incidentId = `INC-${Date.now()}`;

    return {
      id: incidentId,
      title,
      severity,
      status: "investigating",
      startTime: new Date(),
      description: "",
      impact: {
        usersAffected: 0,
        servicesAffected: [],
        businessImpact: "",
      },
      timeline: [
        {
          timestamp: new Date(),
          type: "detection",
          description: "Incident detected and created",
          author: "system",
        },
      ],
      responders: {
        incidentCommander: "",
        technicalLead: "",
        communicationsLead: "",
        stakeholders: [],
      },
      actionItems: [],
    };
  }

  static addTimelineEvent(
    incident: Incident,
    event: Omit<IncidentEvent, "timestamp">
  ): void {
    incident.timeline.push({
      timestamp: new Date(),
      ...event,
    });

    // Auto-update status based on event type
    if (event.type === "resolution") {
      incident.status = "resolved";
      incident.endTime = new Date();
    } else if (event.type === "mitigation") {
      incident.status = "monitoring";
    }
  }

  static escalate(incident: Incident, reason: string): void {
    // Escalation logic based on severity
    const escalationMatrix = {
      P0: ["CTO", "VP Engineering", "CEO"],
      P1: ["Engineering Manager", "VP Engineering"],
      P2: ["Team Lead", "Engineering Manager"],
      P3: ["Team Lead"],
      P4: ["Assignee"],
    };

    const stakeholders = escalationMatrix[incident.severity] || [];
    incident.responders.stakeholders = [
      ...new Set([...incident.responders.stakeholders, ...stakeholders]),
    ];

    this.addTimelineEvent(incident, {
      type: "escalation",
      description: `Incident escalated: ${reason}`,
      author: "system",
    });
  }

  static generateStatusUpdate(incident: Incident): string {
    const duration = incident.endTime
      ? incident.endTime.getTime() - incident.startTime.getTime()
      : Date.now() - incident.startTime.getTime();

    const durationMinutes = Math.floor(duration / (1000 * 60));

    return `
**Incident Status Update - ${incident.id}**

**Title:** ${incident.title}
**Severity:** ${incident.severity}
**Status:** ${incident.status}
**Duration:** ${durationMinutes} minutes
**Users Affected:** ${incident.impact.usersAffected.toLocaleString()}
**Services Affected:** ${incident.impact.servicesAffected.join(", ")}

**Latest Update:**
${
  incident.timeline[incident.timeline.length - 1]?.description ||
  "No updates available"
}

**Next Steps:**
${
  incident.status === "resolved"
    ? "Post-mortem scheduled"
    : "Investigation ongoing"
}
    `.trim();
  }
}
```

#### Post-Mortem Framework

```typescript
interface PostMortem {
  incidentId: string;
  title: string;
  date: Date;
  attendees: string[];
  summary: string;
  timeline: IncidentEvent[];
  rootCauseAnalysis: {
    primaryCause: string;
    contributingFactors: string[];
    whyAnalysis: { question: string; answer: string }[];
  };
  impact: {
    duration: number; // minutes
    usersAffected: number;
    revenueImpact?: number;
    reputationImpact: string;
  };
  whatWentWell: string[];
  whatWentPoorly: string[];
  actionItems: ActionItem[];
  preventionMeasures: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  };
  detectionImprovements: string[];
  responseImprovements: string[];
  lessons: string[];
}

class PostMortemManager {
  static createPostMortem(incident: Incident): PostMortem {
    const duration = incident.endTime
      ? incident.endTime.getTime() - incident.startTime.getTime()
      : 0;

    return {
      incidentId: incident.id,
      title: `Post-Mortem: ${incident.title}`,
      date: new Date(),
      attendees: [],
      summary: "",
      timeline: incident.timeline,
      rootCauseAnalysis: {
        primaryCause: incident.rootCause || "",
        contributingFactors: [],
        whyAnalysis: [],
      },
      impact: {
        duration: Math.floor(duration / (1000 * 60)),
        usersAffected: incident.impact.usersAffected,
        revenueImpact: incident.impact.revenueImpact,
        reputationImpact: "",
      },
      whatWentWell: [],
      whatWentPoorly: [],
      actionItems: incident.actionItems,
      preventionMeasures: {
        immediate: [],
        shortTerm: [],
        longTerm: [],
      },
      detectionImprovements: [],
      responseImprovements: [],
      lessons: [],
    };
  }

  static generateReport(postMortem: PostMortem): string {
    return `
# Post-Mortem Report: ${postMortem.title}

**Date:** ${postMortem.date.toISOString().split("T")[0]}
**Incident ID:** ${postMortem.incidentId}
**Duration:** ${postMortem.impact.duration} minutes
**Users Affected:** ${postMortem.impact.usersAffected.toLocaleString()}

## Executive Summary
${postMortem.summary}

## Timeline
${postMortem.timeline
  .map((event) => `**${event.timestamp.toISOString()}** - ${event.description}`)
  .join("\n")}

## Root Cause Analysis
**Primary Cause:** ${postMortem.rootCauseAnalysis.primaryCause}

**Contributing Factors:**
${postMortem.rootCauseAnalysis.contributingFactors
  .map((factor) => `- ${factor}`)
  .join("\n")}

**5 Whys Analysis:**
${postMortem.rootCauseAnalysis.whyAnalysis
  .map((why, index) => `${index + 1}. ${why.question}\n   ${why.answer}`)
  .join("\n")}

## What Went Well
${postMortem.whatWentWell.map((item) => `- ${item}`).join("\n")}

## What Went Poorly
${postMortem.whatWentPoorly.map((item) => `- ${item}`).join("\n")}

## Action Items
${postMortem.actionItems
  .map(
    (item) =>
      `- [ ] ${item.description} (${item.assignee}, ${
        item.priority
      } priority, due: ${item.dueDate.toISOString().split("T")[0]})`
  )
  .join("\n")}

## Prevention Measures

### Immediate (0-2 weeks)
${postMortem.preventionMeasures.immediate.map((item) => `- ${item}`).join("\n")}

### Short-term (2-8 weeks)
${postMortem.preventionMeasures.shortTerm.map((item) => `- ${item}`).join("\n")}

### Long-term (2+ months)
${postMortem.preventionMeasures.longTerm.map((item) => `- ${item}`).join("\n")}

## Detection Improvements
${postMortem.detectionImprovements.map((item) => `- ${item}`).join("\n")}

## Response Improvements
${postMortem.responseImprovements.map((item) => `- ${item}`).join("\n")}

## Lessons Learned
${postMortem.lessons.map((lesson) => `- ${lesson}`).join("\n")}
    `.trim();
  }

  static trackActionItems(postMortem: PostMortem): {
    completed: number;
    inProgress: number;
    overdue: number;
    total: number;
  } {
    const now = new Date();
    const completed = postMortem.actionItems.filter(
      (item) => item.status === "completed"
    ).length;
    const inProgress = postMortem.actionItems.filter(
      (item) => item.status === "in_progress"
    ).length;
    const overdue = postMortem.actionItems.filter(
      (item) => item.status !== "completed" && item.dueDate < now
    ).length;

    return {
      completed,
      inProgress,
      overdue,
      total: postMortem.actionItems.length,
    };
  }
}
```

#### Incident Metrics and Analysis

```typescript
interface IncidentMetrics {
  period: { start: Date; end: Date };
  totalIncidents: number;
  incidentsBySeverity: { [severity: string]: number };
  meanTimeToDetection: number; // minutes
  meanTimeToResolution: number; // minutes
  meanTimeToRecovery: number; // minutes
  availabilityPercentage: number;
  topCauses: { cause: string; count: number }[];
  trends: {
    frequency: "increasing" | "decreasing" | "stable";
    severity: "increasing" | "decreasing" | "stable";
    resolution: "improving" | "degrading" | "stable";
  };
}

class IncidentAnalytics {
  static calculateMetrics(
    incidents: Incident[],
    period: { start: Date; end: Date }
  ): IncidentMetrics {
    const filteredIncidents = incidents.filter(
      (incident) =>
        incident.startTime >= period.start && incident.startTime <= period.end
    );

    const resolvedIncidents = filteredIncidents.filter(
      (incident) => incident.endTime
    );

    // Calculate MTTD (Mean Time To Detection)
    const detectionTimes = filteredIncidents.map((incident) => {
      const detectionEvent = incident.timeline.find(
        (event) => event.type === "detection"
      );
      return detectionEvent
        ? detectionEvent.timestamp.getTime() - incident.startTime.getTime()
        : 0;
    });
    const mttd =
      detectionTimes.reduce((sum, time) => sum + time, 0) /
      detectionTimes.length /
      (1000 * 60);

    // Calculate MTTR (Mean Time To Resolution)
    const resolutionTimes = resolvedIncidents.map(
      (incident) => incident.endTime!.getTime() - incident.startTime.getTime()
    );
    const mttr =
      resolutionTimes.reduce((sum, time) => sum + time, 0) /
      resolutionTimes.length /
      (1000 * 60);

    // Calculate MTTR (Mean Time To Recovery)
    const recoveryTimes = resolvedIncidents.map((incident) => {
      const recoveryEvent = incident.timeline.find(
        (event) => event.type === "mitigation"
      );
      return recoveryEvent
        ? recoveryEvent.timestamp.getTime() - incident.startTime.getTime()
        : incident.endTime!.getTime() - incident.startTime.getTime();
    });
    const mttr_recovery =
      recoveryTimes.reduce((sum, time) => sum + time, 0) /
      recoveryTimes.length /
      (1000 * 60);

    // Calculate availability
    const totalDowntime = resolutionTimes.reduce((sum, time) => sum + time, 0);
    const periodDuration = period.end.getTime() - period.start.getTime();
    const availability =
      ((periodDuration - totalDowntime) / periodDuration) * 100;

    // Group by severity
    const bySeverity = filteredIncidents.reduce((acc, incident) => {
      acc[incident.severity] = (acc[incident.severity] || 0) + 1;
      return acc;
    }, {} as { [severity: string]: number });

    // Top causes
    const causes = filteredIncidents
      .filter((incident) => incident.rootCause)
      .reduce((acc, incident) => {
        const cause = incident.rootCause!;
        acc[cause] = (acc[cause] || 0) + 1;
        return acc;
      }, {} as { [cause: string]: number });

    const topCauses = Object.entries(causes)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([cause, count]) => ({ cause, count }));

    return {
      period,
      totalIncidents: filteredIncidents.length,
      incidentsBySeverity: bySeverity,
      meanTimeToDetection: mttd,
      meanTimeToResolution: mttr,
      meanTimeToRecovery: mttr_recovery,
      availabilityPercentage: availability,
      topCauses,
      trends: {
        frequency: "stable", // Would need historical data to calculate
        severity: "stable",
        resolution: "stable",
      },
    };
  }

  static generateReport(metrics: IncidentMetrics): string {
    return `
# Incident Metrics Report
**Period:** ${metrics.period.start.toISOString().split("T")[0]} to ${
      metrics.period.end.toISOString().split("T")[0]
    }

## Key Metrics
- **Total Incidents:** ${metrics.totalIncidents}
- **Availability:** ${metrics.availabilityPercentage.toFixed(2)}%
- **MTTD:** ${metrics.meanTimeToDetection.toFixed(1)} minutes
- **MTTR:** ${metrics.meanTimeToResolution.toFixed(1)} minutes
- **MTTR (Recovery):** ${metrics.meanTimeToRecovery.toFixed(1)} minutes

## Incidents by Severity
${Object.entries(metrics.incidentsBySeverity)
  .map(([severity, count]) => `- **${severity}:** ${count}`)
  .join("\n")}

## Top Root Causes
${metrics.topCauses
  .map(({ cause, count }) => `- ${cause}: ${count} incidents`)
  .join("\n")}

## Trends
- **Frequency:** ${metrics.trends.frequency}
- **Severity:** ${metrics.trends.severity}
- **Resolution Time:** ${metrics.trends.resolution}
    `.trim();
  }
}
```

## Real-World Problem Solving Scenarios

### Scenario 1: E-commerce Checkout Failure

**Problem:** 15% of checkout attempts failing during Black Friday sale
**Approach:**

1. **DEFINE:** High checkout failure rate during peak traffic
2. **Root Cause Analysis:** 5 Whys revealed database connection pool exhaustion
3. **Trade-off Analysis:** Immediate scaling vs. long-term architecture changes
4. **Decision:** DACI framework to choose immediate mitigation strategy
5. **Implementation:** Circuit breaker + connection pool scaling + monitoring

### Scenario 2: Microservice Cascade Failure

**Problem:** Single service failure causing system-wide outage
**Approach:**

1. **Distributed Tracing:** Identified failure propagation path
2. **Fault Tree Analysis:** Mapped all failure modes and dependencies
3. **Circuit Breaker Implementation:** Prevented cascade failures
4. **Post-Mortem:** Comprehensive analysis and prevention measures
5. **Architecture Changes:** Implemented bulkhead pattern for isolation

### Scenario 3: Performance Degradation

**Problem:** API response times increasing over time
**Approach:**

1. **Performance Profiling:** Identified database query performance issues
2. **Bottleneck Analysis:** Memory leaks and inefficient caching
3. **Optimization Plan:** Prioritized quick wins vs. long-term improvements
4. **Monitoring Enhancement:** Added comprehensive performance tracking
5. **Continuous Improvement:** Regular performance review process

---

_This comprehensive problem-solving methodology guide equips senior developers with structured approaches to tackle complex technical challenges, from individual debugging to team-based incident response and organizational learning._
