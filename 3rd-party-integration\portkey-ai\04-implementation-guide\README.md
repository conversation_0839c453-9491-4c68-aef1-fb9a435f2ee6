# Implementation Guide

## 🛠️ Step-by-Step Setup & Configuration

Complete guide for implementing Portkey.ai in your AI applications, from initial setup to production deployment.

---

## 📁 Section Contents

### **Getting Started**
- [`setup-guide.md`](./setup-guide.md) - Initial account setup and configuration
- [`environment-setup.md`](./environment-setup.md) - Development environment preparation
- [`first-integration.md`](./first-integration.md) - Your first Portkey.ai API call
- [`authentication.md`](./authentication.md) - API keys and security setup

### **Core Implementation**
- [`basic-routing.md`](./basic-routing.md) - Setting up model routing and fallbacks
- [`prompt-management.md`](./prompt-management.md) - Centralized prompt templates and versioning
- [`caching-setup.md`](./caching-setup.md) - Implementing caching for performance
- [`observability-setup.md`](./observability-setup.md) - Monitoring and analytics configuration

### **Advanced Features**
- [`multi-model-workflows.md`](./multi-model-workflows.md) - Complex AI workflows and orchestration
- [`custom-configs.md`](./custom-configs.md) - Advanced configuration patterns
- [`webhook-integration.md`](./webhook-integration.md) - Event-driven architecture setup
- [`batch-processing.md`](./batch-processing.md) - High-volume request handling

### **Production Deployment**
- [`production-checklist.md`](./production-checklist.md) - Pre-deployment validation
- [`scaling-strategies.md`](./scaling-strategies.md) - Performance and capacity planning
- [`monitoring-alerts.md`](./monitoring-alerts.md) - Production monitoring setup
- [`troubleshooting.md`](./troubleshooting.md) - Common issues and solutions

---

## 🎯 Implementation Roadmap

### **Phase 1: Foundation (Week 1)**
- [ ] Account setup and API key generation
- [ ] Development environment configuration
- [ ] First successful API call
- [ ] Basic routing implementation
- [ ] Simple caching setup

### **Phase 2: Core Features (Week 2-3)**
- [ ] Multi-model routing strategies
- [ ] Prompt template management
- [ ] Observability and monitoring
- [ ] Error handling and fallbacks
- [ ] Rate limiting configuration

### **Phase 3: Advanced Features (Week 4-5)**
- [ ] Custom routing algorithms
- [ ] Advanced caching strategies
- [ ] Webhook integration
- [ ] Batch processing setup
- [ ] Performance optimization

### **Phase 4: Production Ready (Week 6)**
- [ ] Security hardening
- [ ] Production monitoring
- [ ] Load testing and optimization
- [ ] Documentation and training
- [ ] Go-live preparation

---

## 🚀 Quick Start Implementation

### **Step 1: Account Setup**

**Create Portkey.ai Account**:
1. Visit [portkey.ai](https://portkey.ai) and sign up
2. Verify your email address
3. Complete onboarding process
4. Access the dashboard

**Generate API Keys**:
```bash
# In Portkey.ai dashboard:
# 1. Go to API Keys section
# 2. Click "Create New Key"
# 3. Set permissions and rate limits
# 4. Copy the generated key

export PORTKEY_API_KEY="your-portkey-api-key-here"
```

### **Step 2: Environment Setup**

**Install SDK**:
```bash
# Node.js
npm install portkey-ai

# Python
pip install portkey-ai

# Or using yarn
yarn add portkey-ai
```

**Environment Configuration**:
```bash
# .env file
PORTKEY_API_KEY=your-portkey-api-key
PORTKEY_VIRTUAL_KEY=your-virtual-key
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Optional: Custom base URL
PORTKEY_BASE_URL=https://api.portkey.ai/v1
```

### **Step 3: First Integration**

**Basic Setup (Node.js)**:
```javascript
import Portkey from 'portkey-ai';

const portkey = new Portkey({
  apiKey: process.env.PORTKEY_API_KEY,
  virtualKey: process.env.PORTKEY_VIRTUAL_KEY
});

// Your first AI call through Portkey.ai
async function firstCall() {
  try {
    const response = await portkey.chat.completions.create({
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Hello, world!" }
      ],
      model: "gpt-3.5-turbo",
      max_tokens: 100
    });
    
    console.log('Response:', response.choices[0].message.content);
    return response;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

firstCall();
```

**Basic Setup (Python)**:
```python
from portkey_ai import Portkey
import os

portkey = Portkey(
    api_key=os.getenv("PORTKEY_API_KEY"),
    virtual_key=os.getenv("PORTKEY_VIRTUAL_KEY")
)

def first_call():
    try:
        response = portkey.chat.completions.create(
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, world!"}
            ],
            model="gpt-3.5-turbo",
            max_tokens=100
        )
        
        print("Response:", response.choices[0].message.content)
        return response
    except Exception as error:
        print("Error:", error)
        raise error

first_call()
```

---

## 🔧 Core Implementation Steps

### **Step 4: Basic Routing Setup**

**Simple Fallback Configuration**:
```javascript
// Create a config for reliable chat completion
const chatConfig = {
  strategy: { mode: "fallback" },
  targets: [
    { 
      provider: "openai", 
      model: "gpt-4",
      weight: 1
    },
    { 
      provider: "anthropic", 
      model: "claude-2",
      weight: 0  // Fallback only
    },
    { 
      provider: "openai", 
      model: "gpt-3.5-turbo",
      weight: 0  // Last resort
    }
  ],
  retry: {
    attempts: 3,
    backoff: "exponential"
  }
};

// Use the config in your requests
const response = await portkey.chat.completions.create({
  config: chatConfig,
  messages: messages,
  max_tokens: 500
});
```

### **Step 5: Prompt Management**

**Create Prompt Templates**:
```javascript
// Define reusable prompt templates
const promptTemplates = {
  customer_support: {
    template: `You are a helpful customer support agent for {{company_name}}.
    Customer: {{customer_message}}
    Please provide a professional and helpful response.`,
    variables: ["company_name", "customer_message"]
  },
  
  content_writer: {
    template: `Write a {{content_type}} about {{topic}}.
    Target audience: {{audience}}
    Tone: {{tone}}
    Length: {{length}} words`,
    variables: ["content_type", "topic", "audience", "tone", "length"]
  }
};

// Use templates in requests
const response = await portkey.chat.completions.create({
  promptTemplate: "customer_support",
  variables: {
    company_name: "Acme Corp",
    customer_message: "I need help with my order"
  },
  model: "gpt-4"
});
```

### **Step 6: Caching Implementation**

**Enable Intelligent Caching**:
```javascript
const cacheConfig = {
  // Simple caching for exact matches
  simple: {
    mode: "simple",
    ttl: 3600,  // 1 hour
    max_size: "100MB"
  },
  
  // Semantic caching for similar requests
  semantic: {
    mode: "semantic",
    ttl: 7200,  // 2 hours
    similarity_threshold: 0.95,
    embedding_model: "text-embedding-ada-002"
  }
};

// Apply caching to requests
const response = await portkey.chat.completions.create({
  messages: messages,
  model: "gpt-4",
  cache: cacheConfig.semantic,
  metadata: {
    cache_key: "user_chat_session_123"
  }
});
```

### **Step 7: Observability Setup**

**Configure Monitoring**:
```javascript
const observabilityConfig = {
  logging: {
    level: "info",
    include_request: true,
    include_response: false,  // Exclude for privacy
    structured: true
  },
  
  metrics: {
    enabled: true,
    custom_tags: {
      application: "my-ai-app",
      environment: "production",
      version: "1.0.0"
    }
  },
  
  tracing: {
    enabled: true,
    sample_rate: 0.1  // Trace 10% of requests
  }
};

// Add metadata to track requests
const response = await portkey.chat.completions.create({
  messages: messages,
  model: "gpt-4",
  metadata: {
    user_id: "user_123",
    session_id: "session_456",
    feature: "chat_completion",
    version: "v2.1"
  }
});
```

---

## 🔐 Security Implementation

### **API Key Management**

**Secure Key Storage**:
```javascript
// Use environment variables
const portkey = new Portkey({
  apiKey: process.env.PORTKEY_API_KEY,
  virtualKey: process.env.PORTKEY_VIRTUAL_KEY
});

// For production: Use secret management services
// AWS Secrets Manager, Azure Key Vault, etc.
const getSecrets = async () => {
  const secrets = await secretsManager.getSecretValue({
    SecretId: "portkey-api-keys"
  }).promise();
  
  return JSON.parse(secrets.SecretString);
};
```

**Rate Limiting Configuration**:
```javascript
const rateLimitConfig = {
  requests: {
    limit: 1000,
    window: "1h",
    burst: 50
  },
  tokens: {
    limit: 100000,
    window: "1d"
  },
  cost: {
    limit: 50,
    window: "1d",
    currency: "USD"
  }
};
```

### **Content Moderation**

**Input/Output Filtering**:
```javascript
const moderationConfig = {
  input: {
    enabled: true,
    categories: ["hate", "violence", "sexual", "self-harm"],
    action: "block"
  },
  output: {
    enabled: true,
    categories: ["hate", "violence", "sexual", "self-harm"],
    action: "filter"
  },
  custom_filters: [
    {
      pattern: "\\b(password|secret|api[_-]?key)\\b",
      action: "redact",
      replacement: "[REDACTED]"
    }
  ]
};

const response = await portkey.chat.completions.create({
  messages: messages,
  model: "gpt-4",
  moderation: moderationConfig
});
```

---

## 📊 Production Implementation

### **Error Handling**

**Comprehensive Error Management**:
```javascript
class PortkeyService {
  constructor() {
    this.portkey = new Portkey({
      apiKey: process.env.PORTKEY_API_KEY,
      virtualKey: process.env.PORTKEY_VIRTUAL_KEY
    });
  }

  async chatCompletion(messages, options = {}) {
    try {
      const response = await this.portkey.chat.completions.create({
        messages: messages,
        model: options.model || "gpt-3.5-turbo",
        max_tokens: options.maxTokens || 500,
        temperature: options.temperature || 0.7,
        config: options.config,
        metadata: {
          request_id: this.generateRequestId(),
          user_id: options.userId,
          feature: options.feature || "chat"
        }
      });

      return {
        success: true,
        data: response,
        usage: response.usage,
        cost: this.calculateCost(response.usage)
      };

    } catch (error) {
      return this.handleError(error, options);
    }
  }

  handleError(error, options) {
    const errorResponse = {
      success: false,
      error: {
        type: error.type || "unknown_error",
        message: error.message,
        code: error.code,
        request_id: options.requestId
      }
    };

    // Log error for monitoring
    console.error("Portkey API Error:", {
      error: errorResponse.error,
      user_id: options.userId,
      timestamp: new Date().toISOString()
    });

    // Handle specific error types
    switch (error.type) {
      case "rate_limit_exceeded":
        errorResponse.retry_after = error.retry_after;
        break;
      case "insufficient_quota":
        errorResponse.error.message = "Service temporarily unavailable";
        break;
      case "model_unavailable":
        // Try fallback model if available
        if (options.fallbackModel) {
          return this.chatCompletion(messages, {
            ...options,
            model: options.fallbackModel,
            fallbackModel: null
          });
        }
        break;
    }

    return errorResponse;
  }
}
```

### **Performance Monitoring**

**Real-time Metrics Collection**:
```javascript
class MetricsCollector {
  constructor() {
    this.metrics = {
      requests: 0,
      errors: 0,
      latency: [],
      costs: 0
    };
  }

  async trackRequest(requestFn, metadata = {}) {
    const startTime = Date.now();
    this.metrics.requests++;

    try {
      const result = await requestFn();
      
      // Track success metrics
      const latency = Date.now() - startTime;
      this.metrics.latency.push(latency);
      
      if (result.usage) {
        this.metrics.costs += this.calculateCost(result.usage);
      }

      // Send metrics to monitoring service
      this.sendMetrics({
        type: "request_success",
        latency: latency,
        tokens: result.usage?.total_tokens,
        cost: this.calculateCost(result.usage),
        metadata: metadata
      });

      return result;

    } catch (error) {
      this.metrics.errors++;
      
      // Track error metrics
      this.sendMetrics({
        type: "request_error",
        error_type: error.type,
        latency: Date.now() - startTime,
        metadata: metadata
      });

      throw error;
    }
  }

  getMetrics() {
    const latencies = this.metrics.latency;
    return {
      total_requests: this.metrics.requests,
      error_rate: this.metrics.errors / this.metrics.requests,
      avg_latency: latencies.reduce((a, b) => a + b, 0) / latencies.length,
      p95_latency: this.percentile(latencies, 0.95),
      total_cost: this.metrics.costs
    };
  }
}
```

---

## 🧪 Testing Implementation

### **Unit Testing**

**Test Setup with Mocking**:
```javascript
// test/portkey.test.js
import { jest } from '@jest/globals';
import Portkey from 'portkey-ai';

// Mock Portkey for testing
jest.mock('portkey-ai');

describe('Portkey Integration', () => {
  let portkey;
  let mockCreate;

  beforeEach(() => {
    mockCreate = jest.fn();
    Portkey.mockImplementation(() => ({
      chat: {
        completions: {
          create: mockCreate
        }
      }
    }));
    
    portkey = new Portkey({
      apiKey: 'test-api-key',
      virtualKey: 'test-virtual-key'
    });
  });

  test('should handle successful chat completion', async () => {
    const mockResponse = {
      choices: [{ message: { content: 'Hello, world!' } }],
      usage: { total_tokens: 50 }
    };
    
    mockCreate.mockResolvedValue(mockResponse);

    const result = await portkey.chat.completions.create({
      messages: [{ role: 'user', content: 'Hello' }],
      model: 'gpt-3.5-turbo'
    });

    expect(result).toEqual(mockResponse);
    expect(mockCreate).toHaveBeenCalledWith({
      messages: [{ role: 'user', content: 'Hello' }],
      model: 'gpt-3.5-turbo'
    });
  });

  test('should handle API errors gracefully', async () => {
    const mockError = new Error('Rate limit exceeded');
    mockError.type = 'rate_limit_exceeded';
    
    mockCreate.mockRejectedValue(mockError);

    await expect(
      portkey.chat.completions.create({
        messages: [{ role: 'user', content: 'Hello' }],
        model: 'gpt-3.5-turbo'
      })
    ).rejects.toThrow('Rate limit exceeded');
  });
});
```

### **Integration Testing**

**End-to-End Testing**:
```javascript
// test/integration.test.js
describe('Portkey Integration Tests', () => {
  let portkey;

  beforeAll(() => {
    portkey = new Portkey({
      apiKey: process.env.PORTKEY_TEST_API_KEY,
      virtualKey: process.env.PORTKEY_TEST_VIRTUAL_KEY
    });
  });

  test('should complete chat with fallback', async () => {
    const config = {
      strategy: { mode: "fallback" },
      targets: [
        { provider: "openai", model: "gpt-4" },
        { provider: "openai", model: "gpt-3.5-turbo" }
      ]
    };

    const response = await portkey.chat.completions.create({
      config: config,
      messages: [
        { role: "user", content: "Say hello" }
      ],
      max_tokens: 50
    });

    expect(response.choices).toHaveLength(1);
    expect(response.choices[0].message.content).toBeTruthy();
    expect(response.usage.total_tokens).toBeGreaterThan(0);
  }, 30000); // 30 second timeout for API calls
});
```

---

## 📋 Implementation Checklist

### **Development Phase**
- [ ] Portkey.ai account created and verified
- [ ] API keys generated and securely stored
- [ ] SDK installed and configured
- [ ] First successful API call completed
- [ ] Basic routing strategy implemented
- [ ] Error handling added
- [ ] Unit tests written and passing

### **Integration Phase**
- [ ] Prompt templates created and tested
- [ ] Caching strategy implemented
- [ ] Observability configured
- [ ] Rate limiting set up
- [ ] Content moderation enabled
- [ ] Integration tests passing

### **Production Phase**
- [ ] Security review completed
- [ ] Performance testing done
- [ ] Monitoring and alerting configured
- [ ] Documentation updated
- [ ] Team training completed
- [ ] Go-live plan approved

---

**This implementation guide provides a solid foundation for integrating Portkey.ai. Ready to see working code examples?**

**Next**: [Code Examples →](../05-code-examples/README.md)
