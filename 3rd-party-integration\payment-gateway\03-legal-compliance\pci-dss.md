# PCI DSS Compliance

## 🔐 Payment Card Industry Data Security Standard

PCI DSS compliance is mandatory for any organization that stores, processes, or transmits credit card information. Understanding how <PERSON><PERSON> helps and what you're still responsible for is crucial.

---

## 📊 PCI DSS Compliance Levels

### **Merchant Levels by Transaction Volume**

| Level | Annual Transactions | Requirements | Validation |
|-------|-------------------|--------------|------------|
| **Level 1** | 6M+ or breached | Full audit | QSA audit + penetration testing |
| **Level 2** | 1M - 6M | Self-assessment | SAQ + quarterly network scan |
| **Level 3** | 20K - 1M e-commerce | Self-assessment | SAQ + quarterly network scan |
| **Level 4** | <20K e-commerce | Self-assessment | SAQ + quarterly network scan |

### **Your Healthcare Platform Classification**

**Startup Phase** (Level 4):
- <20,000 e-commerce transactions annually
- Self-Assessment Questionnaire (SAQ)
- Quarterly network vulnerability scans
- Annual compliance validation

**Growth Phase** (Level 3):
- 20,000 - 1M e-commerce transactions
- Enhanced SAQ requirements
- Quarterly vulnerability scans
- Potential on-site assessment

**Scale Phase** (Level 2+):
- 1M+ transactions annually
- Qualified Security Assessor (QSA) audit
- Comprehensive penetration testing
- Detailed compliance documentation

---

## 🏗️ PCI DSS Requirements Overview

### **12 Core Requirements**

**Build and Maintain Secure Networks:**
1. **Firewall Configuration**: Protect cardholder data
2. **Default Passwords**: Change vendor-supplied defaults

**Protect Cardholder Data:**
3. **Data Storage**: Protect stored cardholder data
4. **Data Transmission**: Encrypt transmission over public networks

**Maintain Vulnerability Management:**
5. **Antivirus Software**: Use and regularly update
6. **Secure Systems**: Develop and maintain secure systems

**Implement Strong Access Controls:**
7. **Access Restriction**: Restrict access by business need-to-know
8. **Unique IDs**: Assign unique ID to each person with computer access
9. **Physical Access**: Restrict physical access to cardholder data

**Monitor and Test Networks:**
10. **Access Monitoring**: Track and monitor all access to network resources
11. **Security Testing**: Regularly test security systems and processes

**Maintain Information Security Policy:**
12. **Security Policy**: Maintain policy that addresses information security

---

## 🛡️ How Stripe Reduces Your PCI Scope

### **Stripe's PCI Level 1 Certification**

**What Stripe Handles:**
- **Card Data Processing**: All card information processed by Stripe
- **Data Storage**: No card data stored on your servers
- **Tokenization**: Card numbers replaced with secure tokens
- **Encryption**: All data encrypted in transit and at rest
- **Network Security**: Stripe's infrastructure is PCI compliant

**Your Reduced Scope:**
```javascript
// PCI-compliant implementation with Stripe
// Card data never touches your servers

// Frontend: Stripe.js handles card data
const {token, error} = await stripe.createToken(cardElement);

// Backend: Only handle tokens, never raw card data
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000,
  currency: 'usd',
  payment_method: token.id, // Token, not card data
  metadata: {
    appointment_id: 'apt_123' // Non-card data only
  }
});
```

### **SAQ A vs SAQ D Comparison**

**SAQ A (With Stripe)** - Simplified:
- 22 requirements to validate
- Card data handled entirely by Stripe
- Minimal compliance burden
- Annual self-assessment

**SAQ D (Without Stripe)** - Complex:
- 300+ requirements to validate
- Full PCI DSS compliance required
- Extensive security controls
- Potential on-site audits

---

## 📋 Your PCI Compliance Responsibilities

### **Even with Stripe, You Must:**

**1. Secure Your Environment**
```javascript
// Secure server configuration
const secureServer = {
  // HTTPS only
  ssl_certificate: 'valid_ssl_cert',
  force_https: true,
  
  // Secure headers
  security_headers: {
    'Strict-Transport-Security': 'max-age=31536000',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  },
  
  // Access controls
  authentication: 'multi_factor',
  session_management: 'secure_cookies',
  
  // Monitoring
  logging: 'comprehensive',
  monitoring: 'real_time'
};
```

**2. Protect Non-Card Payment Data**
```javascript
// Secure handling of payment-related data
const secureDataHandling = {
  // Encrypt sensitive data
  patient_data: 'encrypted_at_rest',
  doctor_data: 'encrypted_at_rest',
  
  // Secure transmission
  api_calls: 'tls_1_2_minimum',
  database_connections: 'encrypted',
  
  // Access controls
  data_access: 'role_based',
  audit_logging: 'all_access_logged',
  
  // Data retention
  retention_policy: 'minimum_necessary',
  secure_deletion: 'cryptographic_erasure'
};
```

**3. Maintain Secure Development Practices**
```javascript
// Secure coding practices
const secureDevPractices = {
  // Input validation
  validate_all_inputs: true,
  sanitize_outputs: true,
  parameterized_queries: true,
  
  // Authentication & Authorization
  strong_authentication: true,
  session_management: 'secure',
  access_controls: 'principle_of_least_privilege',
  
  // Error handling
  generic_error_messages: true,
  no_sensitive_data_in_logs: true,
  
  // Security testing
  code_reviews: 'mandatory',
  vulnerability_scanning: 'automated',
  penetration_testing: 'annual'
};
```

---

## 🔍 PCI Compliance Implementation

### **Step 1: Environment Assessment**

```javascript
// PCI compliance assessment checklist
const pciAssessment = {
  network_security: {
    firewall_configured: false, // ❌ Needs implementation
    default_passwords_changed: true, // ✅ Complete
    network_segmentation: false, // ❌ Needs implementation
    wireless_security: true // ✅ Complete
  },
  
  data_protection: {
    cardholder_data_inventory: true, // ✅ None stored (Stripe)
    encryption_in_transit: true, // ✅ HTTPS everywhere
    encryption_at_rest: false, // ❌ Needs implementation
    key_management: false // ❌ Needs implementation
  },
  
  access_controls: {
    unique_user_ids: true, // ✅ Complete
    access_restrictions: false, // ❌ Needs implementation
    multi_factor_auth: false, // ❌ Needs implementation
    physical_security: true // ✅ Cloud provider
  },
  
  monitoring: {
    access_logging: false, // ❌ Needs implementation
    log_monitoring: false, // ❌ Needs implementation
    file_integrity_monitoring: false, // ❌ Needs implementation
    vulnerability_scanning: false // ❌ Needs implementation
  }
};
```

### **Step 2: Implementation Plan**

**Phase 1: Critical Security Controls (Week 1-2)**
```javascript
// Immediate security implementations
const criticalControls = {
  // 1. Implement HTTPS everywhere
  ssl_configuration: {
    force_https: true,
    hsts_headers: true,
    secure_cookies: true
  },
  
  // 2. Strong authentication
  authentication: {
    password_policy: {
      min_length: 12,
      complexity: 'high',
      rotation: '90_days'
    },
    multi_factor: 'required_for_admin',
    session_timeout: '15_minutes_idle'
  },
  
  // 3. Access controls
  authorization: {
    role_based_access: true,
    principle_least_privilege: true,
    regular_access_reviews: 'quarterly'
  }
};
```

**Phase 2: Data Protection (Week 3-4)**
```javascript
// Data protection implementation
const dataProtection = {
  // Encryption at rest
  database_encryption: {
    algorithm: 'AES-256',
    key_management: 'aws_kms', // or equivalent
    key_rotation: 'annual'
  },
  
  // Secure data handling
  data_classification: {
    public: 'no_protection',
    internal: 'access_controls',
    confidential: 'encryption_required',
    restricted: 'encryption_plus_audit'
  },
  
  // Data retention
  retention_policy: {
    payment_logs: '7_years',
    user_data: 'until_account_deletion',
    audit_logs: '1_year'
  }
};
```

**Phase 3: Monitoring & Testing (Week 5-6)**
```javascript
// Monitoring and testing implementation
const monitoringControls = {
  // Logging
  security_logging: {
    authentication_events: true,
    authorization_failures: true,
    data_access: true,
    system_changes: true,
    log_retention: '1_year'
  },
  
  // Monitoring
  real_time_monitoring: {
    failed_login_attempts: 'alert_after_5',
    unusual_access_patterns: 'ml_detection',
    system_performance: 'continuous',
    security_events: 'immediate_alert'
  },
  
  // Vulnerability management
  security_testing: {
    vulnerability_scans: 'quarterly',
    penetration_testing: 'annual',
    code_reviews: 'all_releases',
    dependency_scanning: 'continuous'
  }
};
```

---

## 📝 SAQ A Completion Guide

### **Self-Assessment Questionnaire A**

**Requirements for SAQ A Eligibility:**
- ✅ Card data processing outsourced to PCI DSS compliant service provider
- ✅ No electronic storage, processing, or transmission of cardholder data
- ✅ No systems connected to or affecting the cardholder data environment
- ✅ Meets all requirements in SAQ A

**SAQ A Requirements (22 total):**

**Requirement 2: Default Passwords**
```javascript
// Change all vendor-supplied defaults
const secureDefaults = {
  database_passwords: 'changed_from_default',
  admin_accounts: 'renamed_and_secured',
  default_ports: 'changed_where_possible',
  default_configurations: 'hardened'
};
```

**Requirement 8: User Identification**
```javascript
// Unique user identification
const userManagement = {
  unique_user_ids: true,
  no_shared_accounts: true,
  user_access_reviews: 'quarterly',
  inactive_account_removal: '90_days'
};
```

**Requirement 9: Physical Access**
```javascript
// Physical security (cloud environment)
const physicalSecurity = {
  cloud_provider: 'pci_compliant', // AWS, Google Cloud, Azure
  data_center_security: 'provider_managed',
  physical_access_controls: 'provider_managed',
  media_destruction: 'provider_managed'
};
```

---

## 🔧 Technical Implementation

### **Secure Payment Form Implementation**

```html
<!-- PCI-compliant payment form -->
<!DOCTYPE html>
<html>
<head>
  <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
  <form id="payment-form">
    <!-- Stripe Elements handles card data -->
    <div id="card-element">
      <!-- Stripe Elements will create form elements here -->
    </div>
    
    <!-- Your form fields (non-card data) -->
    <input type="hidden" name="appointment_id" value="apt_123">
    <input type="hidden" name="doctor_id" value="doc_456">
    
    <button type="submit">Pay Now</button>
  </form>

  <script>
    const stripe = Stripe('pk_test_...');
    const elements = stripe.elements();
    const cardElement = elements.create('card');
    cardElement.mount('#card-element');

    // Handle form submission
    document.getElementById('payment-form').addEventListener('submit', async (event) => {
      event.preventDefault();
      
      // Create token (card data never touches your server)
      const {token, error} = await stripe.createToken(cardElement);
      
      if (error) {
        console.error('Error:', error);
      } else {
        // Send token to your server (PCI-safe)
        submitPayment(token.id);
      }
    });
  </script>
</body>
</html>
```

### **Secure Backend Implementation**

```javascript
// PCI-compliant backend processing
app.post('/process-payment', async (req, res) => {
  try {
    // Validate input (never trust client data)
    const { token, appointment_id, doctor_id } = req.body;
    
    if (!token || !appointment_id || !doctor_id) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Process payment with Stripe (PCI-compliant)
    const paymentIntent = await stripe.paymentIntents.create({
      amount: 10000,
      currency: 'usd',
      payment_method: token,
      application_fee_amount: 200,
      transfer_data: { destination: doctorAccount },
      metadata: {
        appointment_id: appointment_id,
        doctor_id: doctor_id
        // Never include card data in metadata
      }
    });
    
    // Log transaction (no card data)
    await logTransaction({
      payment_intent_id: paymentIntent.id,
      appointment_id: appointment_id,
      amount: 10000,
      timestamp: new Date()
    });
    
    res.json({ 
      success: true, 
      payment_intent_id: paymentIntent.id 
    });
    
  } catch (error) {
    // Log error (no sensitive data)
    console.error('Payment processing error:', error.message);
    res.status(500).json({ error: 'Payment processing failed' });
  }
});
```

---

## 📊 Compliance Monitoring

### **Ongoing PCI Compliance Tasks**

**Monthly:**
- [ ] Review access logs for unauthorized access
- [ ] Update antivirus definitions
- [ ] Review user access rights
- [ ] Monitor vulnerability scan results

**Quarterly:**
- [ ] Conduct vulnerability scans
- [ ] Review and update security policies
- [ ] Test incident response procedures
- [ ] Review firewall configurations

**Annually:**
- [ ] Complete SAQ assessment
- [ ] Conduct penetration testing (if required)
- [ ] Review and update all security documentation
- [ ] Validate PCI compliance with acquiring bank

### **Compliance Automation**

```javascript
// Automated compliance monitoring
class PCIComplianceMonitor {
  async dailyChecks() {
    const checks = {
      ssl_certificates: await this.checkSSLCertificates(),
      access_logs: await this.reviewAccessLogs(),
      system_updates: await this.checkSystemUpdates(),
      backup_integrity: await this.verifyBackups()
    };
    
    if (checks.ssl_certificates.expiring_soon) {
      await this.alertSecurityTeam('SSL certificates expiring');
    }
    
    return checks;
  }
  
  async quarterlyAssessment() {
    const assessment = {
      vulnerability_scan: await this.runVulnerabilityScans(),
      access_review: await this.reviewUserAccess(),
      policy_review: await this.reviewSecurityPolicies(),
      incident_testing: await this.testIncidentResponse()
    };
    
    await this.generateComplianceReport(assessment);
    return assessment;
  }
}
```

---

## 🚨 Common PCI Compliance Mistakes

### **What NOT to Do**

**❌ Storing Card Data:**
```javascript
// NEVER do this
const payment = {
  card_number: '****************', // ❌ PCI violation
  expiry: '12/25', // ❌ PCI violation
  cvv: '123' // ❌ PCI violation
};
```

**❌ Logging Sensitive Data:**
```javascript
// NEVER log card data
console.log('Processing payment:', {
  card: '****************', // ❌ PCI violation
  amount: 10000
});
```

**❌ Transmitting Card Data:**
```javascript
// NEVER send card data to your server
fetch('/process-payment', {
  method: 'POST',
  body: JSON.stringify({
    card_number: cardNumber, // ❌ PCI violation
    amount: amount
  })
});
```

### **✅ Correct Approaches**

**✅ Use Stripe Tokens:**
```javascript
// Correct: Use tokens instead of card data
const {token} = await stripe.createToken(cardElement);
fetch('/process-payment', {
  method: 'POST',
  body: JSON.stringify({
    token: token.id, // ✅ PCI-safe
    amount: amount
  })
});
```

**✅ Secure Logging:**
```javascript
// Correct: Log without sensitive data
console.log('Processing payment:', {
  payment_intent_id: 'pi_123', // ✅ Safe
  amount: 10000,
  timestamp: new Date()
});
```

---

**PCI DSS compliance with Stripe is manageable and significantly reduces your compliance burden. Next, let's explore regional regulations!**

**Next**: [Regional Regulations →](./regional-regulations.md)
