# Best Practices

## 🏆 Security, Performance & Cost Optimization for AI Applications

Comprehensive guide to implementing Portkey.ai following industry best practices for security, performance, cost management, and monitoring.

---

## 📁 Section Contents

### **Security & Compliance**
- [`security-guidelines.md`](./security-guidelines.md) - API security and data protection
- [`authentication-best-practices.md`](./authentication-best-practices.md) - Secure authentication patterns
- [`data-privacy.md`](./data-privacy.md) - Privacy protection and compliance
- [`content-moderation.md`](./content-moderation.md) - Safe AI content handling

### **Performance Optimization**
- [`caching-strategies.md`](./caching-strategies.md) - Advanced caching for performance
- [`latency-optimization.md`](./latency-optimization.md) - Reducing response times
- [`throughput-scaling.md`](./throughput-scaling.md) - Handling high request volumes
- [`resource-management.md`](./resource-management.md) - Efficient resource utilization

### **Cost Management**
- [`cost-optimization.md`](./cost-optimization.md) - Strategies for minimizing AI costs
- [`budget-management.md`](./budget-management.md) - Budget controls and monitoring
- [`usage-analytics.md`](./usage-analytics.md) - Understanding and optimizing usage
- [`model-selection.md`](./model-selection.md) - Choosing cost-effective models

### **Monitoring & Operations**
- [`observability-best-practices.md`](./observability-best-practices.md) - Comprehensive monitoring
- [`error-handling.md`](./error-handling.md) - Robust error management
- [`alerting-strategies.md`](./alerting-strategies.md) - Proactive issue detection
- [`maintenance-procedures.md`](./maintenance-procedures.md) - Operational maintenance

---

## 🎯 Core Principles

### **1. Security First**
- Never expose API keys in client-side code
- Implement proper authentication and authorization
- Use content moderation for user-generated inputs
- Encrypt sensitive data in transit and at rest
- Regular security audits and updates

### **2. Performance Optimization**
- Implement intelligent caching strategies
- Use appropriate model selection for tasks
- Optimize prompt engineering for efficiency
- Monitor and optimize latency continuously
- Scale resources based on demand

### **3. Cost Efficiency**
- Track costs in real-time across all models
- Implement budget controls and alerts
- Use cost-effective routing strategies
- Optimize token usage through prompt engineering
- Regular cost analysis and optimization

### **4. Reliability & Resilience**
- Implement robust fallback strategies
- Use circuit breakers for failing services
- Design for graceful degradation
- Comprehensive error handling
- Regular disaster recovery testing

### **5. Observability & Monitoring**
- Comprehensive logging and metrics
- Real-time monitoring and alerting
- Performance tracking and analysis
- User experience monitoring
- Business metrics tracking

---

## 🔐 Security Best Practices

### **API Key Management**

**✅ Do:**
```javascript
// Secure API key storage
const portkey = new Portkey({
  apiKey: process.env.PORTKEY_API_KEY,  // Environment variable
  virtualKey: process.env.PORTKEY_VIRTUAL_KEY
});

// Use secret management services in production
const getApiKey = async () => {
  const secret = await secretsManager.getSecretValue({
    SecretId: 'portkey-api-keys'
  }).promise();
  return JSON.parse(secret.SecretString);
};
```

**❌ Don't:**
```javascript
// Never hardcode API keys
const portkey = new Portkey({
  apiKey: "pk_live_1234567890abcdef",  // ❌ Exposed in code
  virtualKey: "vk_1234567890abcdef"    // ❌ Security risk
});

// Never expose keys in client-side code
const clientConfig = {
  apiKey: process.env.PORTKEY_API_KEY  // ❌ Exposed to browser
};
```

### **Input Validation & Sanitization**

**Comprehensive Input Validation:**
```javascript
class InputValidator {
  static validateChatMessage(message) {
    // Length validation
    if (!message || message.length === 0) {
      throw new Error('Message cannot be empty');
    }
    
    if (message.length > 4000) {
      throw new Error('Message too long (max 4000 characters)');
    }
    
    // Content validation
    const suspiciousPatterns = [
      /\b(api[_-]?key|secret|password|token)\b/i,
      /\b(sk-[a-zA-Z0-9]{48})\b/,  // OpenAI API key pattern
      /\b([a-zA-Z0-9]{32,})\b/     // Generic long tokens
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(message)) {
        throw new Error('Message contains potentially sensitive information');
      }
    }
    
    return this.sanitizeInput(message);
  }
  
  static sanitizeInput(input) {
    // Remove potentially harmful content
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
}
```

### **Content Moderation Implementation**

**Multi-Layer Content Filtering:**
```javascript
const moderationConfig = {
  input: {
    enabled: true,
    categories: ["hate", "violence", "sexual", "self-harm", "harassment"],
    action: "block",
    custom_filters: [
      {
        name: "sensitive_data",
        pattern: "\\b(ssn|social security|credit card|password)\\b",
        action: "redact",
        replacement: "[REDACTED]"
      },
      {
        name: "api_keys",
        pattern: "\\b(sk-[a-zA-Z0-9]{48}|pk_[a-zA-Z0-9]{48})\\b",
        action: "block"
      }
    ]
  },
  output: {
    enabled: true,
    categories: ["hate", "violence", "sexual", "self-harm"],
    action: "filter",
    fallback_response: "I cannot provide that type of content. Please try a different request."
  }
};

async function moderatedChatCompletion(messages) {
  try {
    const response = await portkey.chat.completions.create({
      messages: messages,
      model: "gpt-4",
      moderation: moderationConfig,
      metadata: {
        moderation_enabled: true,
        timestamp: new Date().toISOString()
      }
    });
    
    return response;
  } catch (error) {
    if (error.type === 'content_policy_violation') {
      // Log the violation for review
      console.warn('Content policy violation:', {
        user_id: getCurrentUserId(),
        violation_type: error.category,
        timestamp: new Date().toISOString()
      });
      
      return {
        choices: [{
          message: {
            content: "I cannot process that request due to content policy restrictions."
          }
        }]
      };
    }
    throw error;
  }
}
```

---

## ⚡ Performance Optimization

### **Intelligent Caching Strategy**

**Multi-Tier Caching Implementation:**
```javascript
class AdvancedCacheManager {
  constructor() {
    this.memoryCache = new Map();
    this.redisClient = new Redis(process.env.REDIS_URL);
    this.maxMemorySize = 100 * 1024 * 1024; // 100MB
    this.currentMemorySize = 0;
  }
  
  async get(key, options = {}) {
    // L1: Memory cache (fastest)
    if (this.memoryCache.has(key)) {
      const cached = this.memoryCache.get(key);
      if (cached.expires > Date.now()) {
        return cached.data;
      }
      this.memoryCache.delete(key);
    }
    
    // L2: Redis cache (fast)
    if (options.useRedis !== false) {
      const redisData = await this.redisClient.get(key);
      if (redisData) {
        const parsed = JSON.parse(redisData);
        
        // Promote to memory cache if frequently accessed
        if (parsed.accessCount > 5) {
          this.setMemoryCache(key, parsed.data, 300); // 5 minutes
        }
        
        return parsed.data;
      }
    }
    
    return null;
  }
  
  async set(key, data, ttl = 3600, options = {}) {
    const cacheEntry = {
      data: data,
      expires: Date.now() + (ttl * 1000),
      accessCount: 1,
      size: JSON.stringify(data).length
    };
    
    // Store in Redis
    await this.redisClient.setex(
      key, 
      ttl, 
      JSON.stringify(cacheEntry)
    );
    
    // Store in memory if small enough
    if (cacheEntry.size < 1024 * 1024 && // < 1MB
        this.currentMemorySize + cacheEntry.size < this.maxMemorySize) {
      this.setMemoryCache(key, data, Math.min(ttl, 300));
    }
  }
  
  setMemoryCache(key, data, ttl) {
    const entry = {
      data: data,
      expires: Date.now() + (ttl * 1000),
      size: JSON.stringify(data).length
    };
    
    this.memoryCache.set(key, entry);
    this.currentMemorySize += entry.size;
    
    // Cleanup expired entries
    this.cleanupMemoryCache();
  }
  
  cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expires <= now) {
        this.memoryCache.delete(key);
        this.currentMemorySize -= entry.size;
      }
    }
  }
}

// Usage with semantic caching
const cacheManager = new AdvancedCacheManager();

async function cachedChatCompletion(messages, options = {}) {
  // Generate cache key based on content and context
  const cacheKey = generateSemanticCacheKey(messages, options);
  
  // Try cache first
  const cached = await cacheManager.get(cacheKey);
  if (cached) {
    return {
      ...cached,
      cached: true,
      cache_hit: true
    };
  }
  
  // Make API call
  const response = await portkey.chat.completions.create({
    messages: messages,
    model: options.model || "gpt-3.5-turbo",
    cache: {
      mode: "semantic",
      ttl: 3600,
      similarity_threshold: 0.95
    }
  });
  
  // Cache the response
  await cacheManager.set(cacheKey, response, 3600);
  
  return {
    ...response,
    cached: false,
    cache_hit: false
  };
}
```

### **Request Optimization**

**Batch Processing for Efficiency:**
```javascript
class BatchProcessor {
  constructor(options = {}) {
    this.batchSize = options.batchSize || 10;
    this.batchTimeout = options.batchTimeout || 100; // ms
    this.pendingRequests = [];
    this.batchTimer = null;
  }
  
  async process(request) {
    return new Promise((resolve, reject) => {
      this.pendingRequests.push({
        request,
        resolve,
        reject,
        timestamp: Date.now()
      });
      
      // Process batch if full or start timer
      if (this.pendingRequests.length >= this.batchSize) {
        this.processBatch();
      } else if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.processBatch();
        }, this.batchTimeout);
      }
    });
  }
  
  async processBatch() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    
    const batch = this.pendingRequests.splice(0, this.batchSize);
    if (batch.length === 0) return;
    
    try {
      // Process requests concurrently
      const promises = batch.map(({ request }) => 
        this.processSingleRequest(request)
      );
      
      const results = await Promise.allSettled(promises);
      
      // Resolve individual promises
      results.forEach((result, index) => {
        const { resolve, reject } = batch[index];
        
        if (result.status === 'fulfilled') {
          resolve(result.value);
        } else {
          reject(result.reason);
        }
      });
      
    } catch (error) {
      // Reject all pending requests
      batch.forEach(({ reject }) => reject(error));
    }
  }
  
  async processSingleRequest(request) {
    return portkey.chat.completions.create(request);
  }
}
```

---

## 💰 Cost Optimization Strategies

### **Dynamic Model Selection**

**Cost-Aware Routing:**
```javascript
class CostOptimizedRouter {
  constructor() {
    this.modelCosts = {
      "gpt-4": { input: 0.03, output: 0.06 },
      "gpt-3.5-turbo": { input: 0.0015, output: 0.002 },
      "claude-2": { input: 0.008, output: 0.024 },
      "claude-instant": { input: 0.0008, output: 0.0024 }
    };
    
    this.qualityThresholds = {
      simple: 0.7,
      moderate: 0.8,
      high: 0.9
    };
  }
  
  selectModel(request, budget, qualityRequirement = 'moderate') {
    const estimatedTokens = this.estimateTokens(request);
    const threshold = this.qualityThresholds[qualityRequirement];
    
    // Calculate cost for each model
    const modelOptions = Object.entries(this.modelCosts)
      .map(([model, costs]) => ({
        model,
        estimatedCost: this.calculateCost(estimatedTokens, costs),
        quality: this.getModelQuality(model)
      }))
      .filter(option => 
        option.estimatedCost <= budget && 
        option.quality >= threshold
      )
      .sort((a, b) => a.estimatedCost - b.estimatedCost);
    
    return modelOptions[0]?.model || "gpt-3.5-turbo";
  }
  
  calculateCost(tokens, costs) {
    return (tokens.input * costs.input + tokens.output * costs.output) / 1000;
  }
  
  estimateTokens(request) {
    const inputText = request.messages
      .map(m => m.content)
      .join(' ');
    
    // Rough estimation: 1 token ≈ 4 characters
    const inputTokens = Math.ceil(inputText.length / 4);
    const outputTokens = request.max_tokens || 500;
    
    return { input: inputTokens, output: outputTokens };
  }
  
  getModelQuality(model) {
    const qualityScores = {
      "gpt-4": 0.95,
      "claude-2": 0.90,
      "gpt-3.5-turbo": 0.80,
      "claude-instant": 0.75
    };
    
    return qualityScores[model] || 0.5;
  }
}

// Usage
const router = new CostOptimizedRouter();

async function costOptimizedCompletion(request, budget = 0.10) {
  const selectedModel = router.selectModel(request, budget, 'moderate');
  
  const response = await portkey.chat.completions.create({
    ...request,
    model: selectedModel,
    metadata: {
      selected_model: selectedModel,
      budget_limit: budget,
      cost_optimized: true
    }
  });
  
  return response;
}
```

### **Budget Management System**

**Real-time Budget Tracking:**
```javascript
class BudgetManager {
  constructor() {
    this.budgets = new Map();
    this.usage = new Map();
    this.alerts = new Map();
  }
  
  setBudget(userId, period, amount, currency = 'USD') {
    const budgetKey = `${userId}:${period}`;
    this.budgets.set(budgetKey, {
      amount,
      currency,
      period,
      startDate: this.getPeriodStart(period),
      endDate: this.getPeriodEnd(period)
    });
  }
  
  async trackUsage(userId, cost, metadata = {}) {
    const periods = ['daily', 'weekly', 'monthly'];
    
    for (const period of periods) {
      const budgetKey = `${userId}:${period}`;
      const usageKey = `${budgetKey}:${this.getCurrentPeriod(period)}`;
      
      // Update usage
      const currentUsage = this.usage.get(usageKey) || 0;
      const newUsage = currentUsage + cost;
      this.usage.set(usageKey, newUsage);
      
      // Check budget limits
      const budget = this.budgets.get(budgetKey);
      if (budget) {
        const utilizationRate = newUsage / budget.amount;
        
        // Send alerts at different thresholds
        await this.checkBudgetAlerts(userId, period, utilizationRate, newUsage, budget);
        
        // Enforce hard limits
        if (utilizationRate >= 1.0) {
          throw new Error(`Budget exceeded for ${period} period`);
        }
      }
    }
    
    // Log usage for analytics
    await this.logUsage(userId, cost, metadata);
  }
  
  async checkBudgetAlerts(userId, period, utilizationRate, usage, budget) {
    const alertThresholds = [0.5, 0.8, 0.9, 0.95];
    const alertKey = `${userId}:${period}`;
    const lastAlert = this.alerts.get(alertKey) || 0;
    
    for (const threshold of alertThresholds) {
      if (utilizationRate >= threshold && lastAlert < threshold) {
        await this.sendBudgetAlert(userId, period, {
          threshold: threshold * 100,
          usage: usage,
          budget: budget.amount,
          remaining: budget.amount - usage
        });
        
        this.alerts.set(alertKey, threshold);
        break;
      }
    }
  }
  
  async sendBudgetAlert(userId, period, alertData) {
    console.log(`Budget Alert for ${userId}:`, {
      period,
      threshold: `${alertData.threshold}%`,
      usage: `$${alertData.usage.toFixed(2)}`,
      budget: `$${alertData.budget.toFixed(2)}`,
      remaining: `$${alertData.remaining.toFixed(2)}`
    });
    
    // In production, send email/SMS/webhook notification
    // await notificationService.send(userId, 'budget_alert', alertData);
  }
  
  getBudgetStatus(userId, period) {
    const budgetKey = `${userId}:${period}`;
    const usageKey = `${budgetKey}:${this.getCurrentPeriod(period)}`;
    
    const budget = this.budgets.get(budgetKey);
    const usage = this.usage.get(usageKey) || 0;
    
    if (!budget) {
      return { error: 'No budget set for this period' };
    }
    
    return {
      budget: budget.amount,
      usage: usage,
      remaining: budget.amount - usage,
      utilization: (usage / budget.amount) * 100,
      period: period,
      currency: budget.currency
    };
  }
}
```

---

## 📊 Monitoring & Observability

### **Comprehensive Metrics Collection**

**Multi-Dimensional Monitoring:**
```javascript
class MetricsCollector {
  constructor() {
    this.metrics = {
      requests: new Map(),
      latency: [],
      errors: new Map(),
      costs: new Map(),
      models: new Map(),
      users: new Map()
    };
  }
  
  async trackRequest(requestData, response, error = null) {
    const timestamp = Date.now();
    const requestId = requestData.metadata?.request_id || this.generateId();
    
    // Basic metrics
    this.incrementCounter('requests.total');
    this.incrementCounter(`requests.model.${requestData.model}`);
    
    if (error) {
      this.incrementCounter('requests.errors');
      this.incrementCounter(`errors.type.${error.type}`);
      this.trackError(error, requestData);
    } else {
      this.incrementCounter('requests.success');
      this.trackLatency(response.latency || 0);
      this.trackCost(response.usage, requestData.model);
      this.trackTokens(response.usage);
    }
    
    // User-specific metrics
    if (requestData.metadata?.user_id) {
      this.trackUserMetrics(requestData.metadata.user_id, response, error);
    }
    
    // Feature-specific metrics
    if (requestData.metadata?.feature) {
      this.trackFeatureMetrics(requestData.metadata.feature, response, error);
    }
    
    // Send to monitoring service
    await this.sendMetrics({
      timestamp,
      request_id: requestId,
      model: requestData.model,
      success: !error,
      latency: response?.latency,
      tokens: response?.usage?.total_tokens,
      cost: this.calculateCost(response?.usage, requestData.model),
      user_id: requestData.metadata?.user_id,
      feature: requestData.metadata?.feature
    });
  }
  
  trackLatency(latency) {
    this.metrics.latency.push({
      value: latency,
      timestamp: Date.now()
    });
    
    // Keep only last 1000 measurements
    if (this.metrics.latency.length > 1000) {
      this.metrics.latency = this.metrics.latency.slice(-1000);
    }
  }
  
  getLatencyPercentiles() {
    const latencies = this.metrics.latency
      .map(l => l.value)
      .sort((a, b) => a - b);
    
    if (latencies.length === 0) return {};
    
    return {
      p50: this.percentile(latencies, 0.5),
      p90: this.percentile(latencies, 0.9),
      p95: this.percentile(latencies, 0.95),
      p99: this.percentile(latencies, 0.99),
      avg: latencies.reduce((a, b) => a + b, 0) / latencies.length
    };
  }
  
  percentile(arr, p) {
    const index = Math.ceil(arr.length * p) - 1;
    return arr[index];
  }
  
  generateHealthReport() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    const recentLatencies = this.metrics.latency
      .filter(l => l.timestamp > oneHourAgo);
    
    const totalRequests = this.getCounter('requests.total');
    const errorRequests = this.getCounter('requests.errors');
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
    
    return {
      timestamp: new Date().toISOString(),
      status: this.determineHealthStatus(errorRate, recentLatencies),
      metrics: {
        total_requests: totalRequests,
        error_rate: errorRate,
        latency: this.getLatencyPercentiles(),
        uptime: this.calculateUptime()
      },
      alerts: this.getActiveAlerts()
    };
  }
  
  determineHealthStatus(errorRate, latencies) {
    if (errorRate > 5) return 'critical';
    if (errorRate > 1) return 'warning';
    
    const avgLatency = latencies.length > 0 
      ? latencies.reduce((a, b) => a + b.value, 0) / latencies.length 
      : 0;
    
    if (avgLatency > 5000) return 'warning';
    if (avgLatency > 10000) return 'critical';
    
    return 'healthy';
  }
}
```

---

## 🚨 Error Handling & Resilience

### **Comprehensive Error Management**

**Production-Ready Error Handling:**
```javascript
class ErrorHandler {
  constructor() {
    this.retryStrategies = {
      'rate_limit_exceeded': {
        maxRetries: 5,
        backoff: 'exponential',
        baseDelay: 1000,
        maxDelay: 30000
      },
      'timeout': {
        maxRetries: 3,
        backoff: 'linear',
        baseDelay: 1000
      },
      'server_error': {
        maxRetries: 3,
        backoff: 'exponential',
        baseDelay: 2000
      }
    };
    
    this.circuitBreakers = new Map();
  }
  
  async handleRequest(requestFn, options = {}) {
    const maxRetries = options.maxRetries || 3;
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Check circuit breaker
        if (this.isCircuitOpen(options.provider)) {
          throw new Error('Circuit breaker is open');
        }
        
        const result = await requestFn();
        
        // Reset circuit breaker on success
        this.recordSuccess(options.provider);
        
        return result;
        
      } catch (error) {
        lastError = error;
        
        // Record failure for circuit breaker
        this.recordFailure(options.provider);
        
        // Check if error is retryable
        if (!this.isRetryableError(error) || attempt === maxRetries) {
          break;
        }
        
        // Calculate delay for next attempt
        const delay = this.calculateDelay(error.type, attempt);
        await this.sleep(delay);
        
        console.log(`Retry attempt ${attempt + 1} after ${delay}ms for error: ${error.message}`);
      }
    }
    
    // All retries exhausted
    throw this.enhanceError(lastError, maxRetries);
  }
  
  isRetryableError(error) {
    const retryableTypes = [
      'rate_limit_exceeded',
      'timeout',
      'server_error',
      'network_error',
      'service_unavailable'
    ];
    
    return retryableTypes.includes(error.type);
  }
  
  calculateDelay(errorType, attempt) {
    const strategy = this.retryStrategies[errorType] || this.retryStrategies.timeout;
    
    let delay = strategy.baseDelay;
    
    if (strategy.backoff === 'exponential') {
      delay = strategy.baseDelay * Math.pow(2, attempt);
    } else if (strategy.backoff === 'linear') {
      delay = strategy.baseDelay * (attempt + 1);
    }
    
    // Add jitter to prevent thundering herd
    delay += Math.random() * 1000;
    
    return Math.min(delay, strategy.maxDelay || 30000);
  }
  
  // Circuit breaker implementation
  isCircuitOpen(provider) {
    const breaker = this.circuitBreakers.get(provider);
    if (!breaker) return false;
    
    const now = Date.now();
    
    // Check if circuit should be half-open
    if (breaker.state === 'open' && 
        now - breaker.lastFailure > breaker.timeout) {
      breaker.state = 'half-open';
      breaker.halfOpenAttempts = 0;
    }
    
    return breaker.state === 'open';
  }
  
  recordFailure(provider) {
    let breaker = this.circuitBreakers.get(provider);
    
    if (!breaker) {
      breaker = {
        failures: 0,
        successes: 0,
        state: 'closed',
        threshold: 5,
        timeout: 60000, // 1 minute
        lastFailure: Date.now()
      };
      this.circuitBreakers.set(provider, breaker);
    }
    
    breaker.failures++;
    breaker.lastFailure = Date.now();
    
    if (breaker.state === 'half-open') {
      breaker.state = 'open';
    } else if (breaker.failures >= breaker.threshold) {
      breaker.state = 'open';
      console.warn(`Circuit breaker opened for provider: ${provider}`);
    }
  }
  
  recordSuccess(provider) {
    const breaker = this.circuitBreakers.get(provider);
    if (!breaker) return;
    
    breaker.successes++;
    
    if (breaker.state === 'half-open') {
      breaker.halfOpenAttempts++;
      
      if (breaker.halfOpenAttempts >= 3) {
        breaker.state = 'closed';
        breaker.failures = 0;
        console.log(`Circuit breaker closed for provider: ${provider}`);
      }
    }
  }
  
  enhanceError(error, retryCount) {
    return {
      ...error,
      retryCount,
      timestamp: new Date().toISOString(),
      enhanced: true
    };
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

---

**These best practices ensure your Portkey.ai integration is secure, performant, cost-effective, and reliable. Following these guidelines will help you build production-ready AI applications that scale effectively.**

**Implementation of these best practices is crucial for successful AI application deployment. Start with security fundamentals and gradually implement performance and cost optimizations as your application grows.**
