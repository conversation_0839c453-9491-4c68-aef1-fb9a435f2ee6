# Git Internals: Data Model and Object Storage

Understanding Git's internal data model is crucial for senior developers and technical leaders. This knowledge enables better decision-making, effective troubleshooting, and confident use of advanced Git features.

## Git's Object Database

### The Four Object Types

Git stores all data as objects in a content-addressable filesystem. Every object is identified by a SHA-1 hash of its content.

```typescript
interface GitObject {
  type: 'blob' | 'tree' | 'commit' | 'tag';
  size: number;
  content: Buffer;
  sha1: string; // 40-character hexadecimal hash
}

// Example object structure
interface BlobObject extends GitObject {
  type: 'blob';
  content: Buffer; // Raw file content
}

interface TreeObject extends GitObject {
  type: 'tree';
  entries: TreeEntry[];
}

interface TreeEntry {
  mode: string;    // File permissions (e.g., '100644', '040000')
  name: string;    // Filename or directory name
  sha1: string;    // SHA-1 hash of the object
  type: 'blob' | 'tree';
}

interface CommitObject extends GitObject {
  type: 'commit';
  tree: string;        // SHA-1 of root tree
  parents: string[];   // SHA-1s of parent commits
  author: GitSignature;
  committer: GitSignature;
  message: string;
}

interface GitSignature {
  name: string;
  email: string;
  timestamp: number;
  timezone: string;
}
```

### Object Storage Deep Dive

#### 1. Blob Objects (File Content)

```bash
# Create a simple file and see how Git stores it
echo "Hello, Git internals!" > example.txt
git add example.txt

# Find the blob object
git ls-files -s example.txt
# Output: 100644 a1b2c3d4... 0    example.txt

# Examine the blob content
git cat-file -p a1b2c3d4
# Output: Hello, Git internals!

# See the object type and size
git cat-file -t a1b2c3d4  # blob
git cat-file -s a1b2c3d4  # 20 (bytes)
```

#### 2. Tree Objects (Directory Structure)

```bash
# Create a directory structure
mkdir src
echo "console.log('Hello');" > src/index.js
echo "export const version = '1.0.0';" > src/version.js
git add .
git commit -m "Add source files"

# Examine the tree structure
git cat-file -p HEAD^{tree}
# Output:
# 100644 blob a1b2c3d4... example.txt
# 040000 tree e5f6g7h8... src

# Examine the src tree
git cat-file -p e5f6g7h8
# Output:
# 100644 blob i9j0k1l2... index.js
# 100644 blob m3n4o5p6... version.js
```

#### 3. Commit Objects (Snapshots)

```bash
# Examine a commit object
git cat-file -p HEAD
# Output:
# tree e5f6g7h8...
# parent q7r8s9t0...
# <AUTHOR> <EMAIL> 1640995200 +0000
# <AUTHOR> <EMAIL> 1640995200 +0000
#
# Add source files
```

### The .git Directory Structure

```typescript
interface GitRepository {
  gitDir: string; // Usually .git/
  structure: {
    objects: {
      path: '.git/objects/';
      description: 'Object database (blobs, trees, commits, tags)';
      subdirectories: {
        info: 'Object metadata';
        pack: 'Packed objects for efficiency';
        [hash_prefix]: 'Loose objects (first 2 chars of SHA-1)';
      };
    };
    
    refs: {
      path: '.git/refs/';
      description: 'References to commits (branches, tags)';
      subdirectories: {
        heads: 'Local branches';
        remotes: 'Remote tracking branches';
        tags: 'Tag references';
      };
    };
    
    HEAD: {
      path: '.git/HEAD';
      description: 'Current branch or commit reference';
      content: 'ref: refs/heads/main' | string; // SHA-1 for detached HEAD
    };
    
    index: {
      path: '.git/index';
      description: 'Staging area (binary file)';
      purpose: 'Tracks files staged for next commit';
    };
    
    config: {
      path: '.git/config';
      description: 'Repository-specific configuration';
      sections: ['core', 'remote', 'branch', 'user'];
    };
    
    hooks: {
      path: '.git/hooks/';
      description: 'Scripts triggered by Git events';
      examples: ['pre-commit', 'post-receive', 'pre-push'];
    };
  };
}
```

### Practical Exploration Commands

```bash
# Explore your repository's internals
cd your-project

# 1. See all objects in the repository
find .git/objects -type f | head -10

# 2. Examine object types
for obj in $(git rev-list --objects --all | cut -d' ' -f1 | head -5); do
  echo "Object: $obj Type: $(git cat-file -t $obj)"
done

# 3. See the current repository state
echo "HEAD points to: $(cat .git/HEAD)"
echo "Current commit: $(git rev-parse HEAD)"
echo "Current tree: $(git rev-parse HEAD^{tree})"

# 4. Explore the index (staging area)
git ls-files --stage

# 5. See packed vs loose objects
echo "Loose objects: $(find .git/objects -name '[0-9a-f][0-9a-f]' | wc -l)"
echo "Pack files: $(find .git/objects/pack -name '*.pack' | wc -l)"
```

## Content-Addressable Storage Benefits

### 1. Data Integrity

```typescript
interface DataIntegrity {
  principle: 'Content determines identity';
  benefits: [
    'Automatic corruption detection',
    'Impossible to modify history without detection',
    'Cryptographic verification of data integrity',
    'Distributed verification across repositories'
  ];
  
  example: {
    scenario: 'File corruption detection';
    process: [
      'Git calculates SHA-1 of file content',
      'Compares with stored SHA-1 in tree object',
      'Mismatch indicates corruption',
      'Git can restore from other sources'
    ];
  };
}
```

### 2. Efficient Storage

```typescript
interface StorageEfficiency {
  deduplication: {
    principle: 'Identical content stored once';
    example: 'Same file in multiple branches = single blob object';
    benefit: 'Significant space savings in large repositories';
  };
  
  deltaCompression: {
    principle: 'Similar objects stored as deltas';
    implementation: 'Pack files compress related objects';
    benefit: 'Reduced storage and transfer size';
  };
  
  packFiles: {
    trigger: 'git gc or git repack';
    process: [
      'Identify related objects',
      'Compute deltas between similar objects',
      'Store base object + deltas in pack file',
      'Create index for fast access'
    ];
  };
}
```

### 3. Distributed Architecture

```typescript
interface DistributedBenefits {
  replication: {
    principle: 'Every clone is a complete backup';
    benefit: 'No single point of failure';
    implication: 'Any repository can serve as authoritative source';
  };
  
  verification: {
    principle: 'SHA-1 hashes enable verification';
    process: [
      'Clone repository from any source',
      'Verify object integrity using SHA-1',
      'Detect any tampering or corruption',
      'Trust content based on cryptographic proof'
    ];
  };
  
  synchronization: {
    principle: 'Objects are immutable and uniquely identified';
    benefit: 'Efficient synchronization between repositories';
    process: [
      'Compare object lists between repositories',
      'Transfer only missing objects',
      'Verify transferred objects using SHA-1'
    ];
  };
}
```

## Advanced Object Manipulation

### Creating Objects Manually

```bash
# Create a blob object manually
echo "Manual blob content" | git hash-object -w --stdin
# Output: SHA-1 hash of the created blob

# Create a tree object manually
git mktree << EOF
100644 blob $(echo "file1 content" | git hash-object -w --stdin)	file1.txt
100644 blob $(echo "file2 content" | git hash-object -w --stdin)	file2.txt
040000 tree $(git mktree < /dev/null)	empty-dir
EOF

# Create a commit object manually (advanced)
tree_sha=$(git mktree << EOF
100644 blob $(echo "Hello" | git hash-object -w --stdin)	hello.txt
EOF)

commit_sha=$(git commit-tree $tree_sha -m "Manual commit")
echo "Created commit: $commit_sha"
```

### Object Inspection Tools

```typescript
interface GitInspectionTools {
  catFile: {
    command: 'git cat-file';
    options: {
      '-p': 'Pretty-print object content';
      '-t': 'Show object type';
      '-s': 'Show object size';
      '-e': 'Exit with zero if object exists';
    };
    examples: [
      'git cat-file -p HEAD',
      'git cat-file -t HEAD^{tree}',
      'git cat-file -s HEAD:README.md'
    ];
  };
  
  lsTree: {
    command: 'git ls-tree';
    purpose: 'List tree object contents';
    options: {
      '-r': 'Recurse into subdirectories';
      '-t': 'Show tree objects';
      '--name-only': 'Show only names';
    };
    examples: [
      'git ls-tree HEAD',
      'git ls-tree -r HEAD',
      'git ls-tree HEAD src/'
    ];
  };
  
  revList: {
    command: 'git rev-list';
    purpose: 'List commit objects';
    options: {
      '--objects': 'Include non-commit objects';
      '--all': 'Include all refs';
      '--since': 'Commits since date';
    };
    examples: [
      'git rev-list HEAD',
      'git rev-list --objects HEAD',
      'git rev-list --since="1 week ago" HEAD'
    ];
  };
}
```

## Performance Implications

### Object Storage Optimization

```bash
# Check repository size and object count
git count-objects -v

# Optimize repository storage
git gc --aggressive --prune=now

# Analyze pack file efficiency
git verify-pack -v .git/objects/pack/*.idx | head -20

# Find large objects
git rev-list --objects --all | \
  git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
  awk '/^blob/ {print substr($0,6)}' | \
  sort --numeric-sort --key=2 | \
  tail -10
```

### Best Practices for Object Management

```typescript
interface ObjectManagementBestPractices {
  avoidLargeFiles: {
    principle: 'Git is optimized for text files';
    problems: [
      'Large binary files bloat repository',
      'Poor delta compression for binaries',
      'Slow clone and fetch operations'
    ];
    solutions: [
      'Use Git LFS for large files',
      'Store binaries in external storage',
      'Use .gitignore for build artifacts'
    ];
  };
  
  regularMaintenance: {
    commands: [
      'git gc --auto',      // Automatic garbage collection
      'git prune',          // Remove unreachable objects
      'git fsck',           // Verify object integrity
      'git repack -ad'      // Repack all objects
    ];
    frequency: 'Weekly for active repositories';
  };
  
  monitoringMetrics: {
    repositorySize: 'git count-objects -vH';
    packEfficiency: 'Ratio of packed to loose objects';
    largestObjects: 'Identify files that should use Git LFS';
    integrityChecks: 'Regular git fsck execution';
  };
}
```

---

*Understanding Git's internal object model provides the foundation for advanced Git usage, effective troubleshooting, and informed decision-making about repository management strategies.*
