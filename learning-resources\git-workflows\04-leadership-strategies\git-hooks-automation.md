# Git Hooks for Automation and Quality Gates

Git hooks provide powerful automation capabilities for technical leaders to enforce quality standards, automate workflows, and maintain consistency across cross-functional teams.

## Git Hooks Overview

### Hook Types and Triggers

```typescript
interface GitHooks {
  clientSide: {
    preCommit: {
      trigger: 'Before commit is created';
      useCases: [
        'Code formatting and linting',
        'Running unit tests',
        'Security scanning',
        'Commit message validation'
      ];
      canAbort: true;
    };
    
    prepareCommitMsg: {
      trigger: 'Before commit message editor opens';
      useCases: [
        'Auto-generate commit message templates',
        'Add issue numbers from branch names',
        'Include change summaries'
      ];
      canAbort: false;
    };
    
    commitMsg: {
      trigger: 'After commit message is entered';
      useCases: [
        'Validate commit message format',
        'Enforce conventional commits',
        'Check for required information'
      ];
      canAbort: true;
    };
    
    preRebase: {
      trigger: 'Before rebase operation';
      useCases: [
        'Prevent rebasing published commits',
        'Validate rebase safety',
        'Backup current state'
      ];
      canAbort: true;
    };
    
    prePush: {
      trigger: 'Before push to remote';
      useCases: [
        'Run full test suite',
        'Security vulnerability scanning',
        'Performance regression testing',
        'Branch protection validation'
      ];
      canAbort: true;
    };
  };
  
  serverSide: {
    preReceive: {
      trigger: 'Before any refs are updated';
      useCases: [
        'Authentication and authorization',
        'Global repository policies',
        'Backup creation'
      ];
      canAbort: true;
    };
    
    update: {
      trigger: 'Before each ref is updated';
      useCases: [
        'Branch-specific policies',
        'Force push prevention',
        'File size limits'
      ];
      canAbort: true;
    };
    
    postReceive: {
      trigger: 'After all refs are updated';
      useCases: [
        'Deployment triggers',
        'Notification sending',
        'Documentation updates',
        'Issue tracking integration'
      ];
      canAbort: false;
    };
  };
}
```

## Client-Side Hook Implementations

### Pre-Commit Hook for Code Quality

```bash
#!/bin/sh
# .git/hooks/pre-commit

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "${YELLOW}Running pre-commit checks...${NC}"

# Check if this is an initial commit
if git rev-parse --verify HEAD >/dev/null 2>&1
then
    against=HEAD
else
    # Initial commit: diff against an empty tree object
    against=$(git hash-object -t tree /dev/null)
fi

# Redirect output to stderr
exec 1>&2

# Check for whitespace errors
if ! git diff-index --check --cached $against --
then
    echo "${RED}Error: Whitespace errors found. Please fix them before committing.${NC}"
    exit 1
fi

# Get list of staged files
staged_files=$(git diff --cached --name-only --diff-filter=ACM)

# Run linting for JavaScript/TypeScript files
js_files=$(echo "$staged_files" | grep -E '\.(js|ts|jsx|tsx)$' || true)
if [ -n "$js_files" ]; then
    echo "Linting JavaScript/TypeScript files..."
    if ! npx eslint $js_files; then
        echo "${RED}ESLint failed. Please fix linting errors before committing.${NC}"
        exit 1
    fi
    
    # Auto-format with Prettier
    echo "Formatting code with Prettier..."
    npx prettier --write $js_files
    git add $js_files
fi

# Run linting for Python files
py_files=$(echo "$staged_files" | grep -E '\.py$' || true)
if [ -n "$py_files" ]; then
    echo "Linting Python files..."
    if ! python -m flake8 $py_files; then
        echo "${RED}Flake8 failed. Please fix linting errors before committing.${NC}"
        exit 1
    fi
    
    # Auto-format with Black
    echo "Formatting Python code with Black..."
    python -m black $py_files
    git add $py_files
fi

# Run unit tests for affected modules
echo "Running unit tests..."
if [ -f "package.json" ]; then
    if ! npm test -- --passWithNoTests; then
        echo "${RED}Unit tests failed. Please fix failing tests before committing.${NC}"
        exit 1
    fi
elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
    if ! python -m pytest tests/unit/ -x; then
        echo "${RED}Unit tests failed. Please fix failing tests before committing.${NC}"
        exit 1
    fi
fi

# Security scanning for sensitive data
echo "Scanning for sensitive data..."
if git diff --cached --name-only | xargs grep -l -E "(password|secret|key|token)" >/dev/null 2>&1; then
    echo "${YELLOW}Warning: Potential sensitive data detected. Please review:${NC}"
    git diff --cached --name-only | xargs grep -n -E "(password|secret|key|token)" || true
    echo "${YELLOW}If this is intentional, commit with --no-verify${NC}"
    exit 1
fi

echo "${GREEN}All pre-commit checks passed!${NC}"
exit 0
```

### Commit Message Hook

```bash
#!/bin/sh
# .git/hooks/commit-msg

# Read the commit message
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "Invalid commit message format!"
    echo "Format: type(scope): description"
    echo "Types: feat, fix, docs, style, refactor, test, chore"
    echo "Example: feat(auth): add OAuth2 integration"
    echo "Example: fix(payment): handle invalid card numbers"
    exit 1
fi

# Check commit message length
if [ $(head -n1 "$1" | wc -c) -gt 72 ]; then
    echo "Commit message first line too long (max 72 characters)"
    exit 1
fi

exit 0
```

### Pre-Push Hook for Integration Testing

```bash
#!/bin/sh
# .git/hooks/pre-push

protected_branch='main'
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

echo "Running pre-push checks for branch: $current_branch"

# If pushing to protected branch, run full test suite
if [ "$current_branch" = "$protected_branch" ]; then
    echo "Pushing to protected branch. Running full test suite..."
    
    # Run all tests
    if [ -f "package.json" ]; then
        npm run test:all || exit 1
        npm run test:e2e || exit 1
    fi
    
    # Run security audit
    if [ -f "package.json" ]; then
        npm audit --audit-level moderate || exit 1
    fi
    
    # Check for large files
    large_files=$(git diff --cached --name-only | xargs ls -la | awk '$5 > 1048576 {print $9, $5}')
    if [ -n "$large_files" ]; then
        echo "Large files detected (>1MB):"
        echo "$large_files"
        echo "Consider using Git LFS for large files"
        exit 1
    fi
fi

# Performance regression testing
if [ -f "performance-tests.js" ]; then
    echo "Running performance regression tests..."
    npm run test:performance || exit 1
fi

echo "All pre-push checks passed!"
exit 0
```

## Server-Side Hook Implementations

### Pre-Receive Hook for Repository Policies

```bash
#!/bin/sh
# hooks/pre-receive

# Read all ref updates
while read oldrev newrev refname; do
    # Extract branch name
    branch=$(echo $refname | sed -e 's,.*/\(.*\),\1,')
    
    # Protect main branch from force pushes
    if [ "$branch" = "main" ]; then
        if [ "$oldrev" != "0000000000000000000000000000000000000000" ]; then
            # Check if this is a force push
            if ! git merge-base --is-ancestor $oldrev $newrev; then
                echo "Error: Force push to main branch is not allowed"
                exit 1
            fi
        fi
    fi
    
    # Check file size limits
    if [ "$newrev" != "0000000000000000000000000000000000000000" ]; then
        # Check for files larger than 50MB
        large_files=$(git diff --name-only $oldrev..$newrev | \
                     xargs git cat-file -s | \
                     awk '$1 > 52428800 {print $2}')
        
        if [ -n "$large_files" ]; then
            echo "Error: Files larger than 50MB detected:"
            echo "$large_files"
            echo "Please use Git LFS for large files"
            exit 1
        fi
    fi
    
    # Validate commit messages in the push
    for commit in $(git rev-list $oldrev..$newrev); do
        message=$(git log --format=%B -n 1 $commit)
        if ! echo "$message" | grep -qE '^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'; then
            echo "Error: Invalid commit message format in $commit"
            echo "Message: $message"
            exit 1
        fi
    done
done

exit 0
```

### Post-Receive Hook for Automation

```bash
#!/bin/sh
# hooks/post-receive

# Read all ref updates
while read oldrev newrev refname; do
    branch=$(echo $refname | sed -e 's,.*/\(.*\),\1,')
    
    # Trigger deployment for main branch
    if [ "$branch" = "main" ]; then
        echo "Triggering deployment for main branch..."
        
        # Trigger CI/CD pipeline
        curl -X POST \
             -H "Authorization: token $CI_TOKEN" \
             -H "Content-Type: application/json" \
             -d '{"ref":"main","inputs":{"environment":"staging"}}' \
             "$CI_WEBHOOK_URL"
        
        # Send notification to team
        curl -X POST \
             -H "Content-Type: application/json" \
             -d "{\"text\":\"New deployment triggered for main branch. Commit: $newrev\"}" \
             "$SLACK_WEBHOOK_URL"
    fi
    
    # Auto-create release for version tags
    if echo "$refname" | grep -q "refs/tags/v"; then
        version=$(echo $refname | sed -e 's,refs/tags/,,')
        echo "Creating release for version $version..."
        
        # Generate release notes
        if [ "$oldrev" != "0000000000000000000000000000000000000000" ]; then
            git log --pretty=format:"- %s" $oldrev..$newrev > /tmp/release-notes.txt
        else
            echo "Initial release" > /tmp/release-notes.txt
        fi
        
        # Create GitHub release
        curl -X POST \
             -H "Authorization: token $GITHUB_TOKEN" \
             -H "Content-Type: application/json" \
             -d "{\"tag_name\":\"$version\",\"name\":\"Release $version\",\"body\":\"$(cat /tmp/release-notes.txt)\"}" \
             "https://api.github.com/repos/$GITHUB_REPO/releases"
    fi
done

exit 0
```

## Cross-Functional Team Hook Strategies

### Discipline-Specific Quality Gates

```typescript
interface DisciplineSpecificHooks {
  backend: {
    preCommit: [
      'API schema validation',
      'Database migration testing',
      'Security vulnerability scanning',
      'Performance regression testing'
    ];
    
    prePush: [
      'Integration test execution',
      'API contract testing',
      'Load testing for critical endpoints',
      'Documentation generation'
    ];
  };
  
  frontend: {
    preCommit: [
      'Component library consistency',
      'Accessibility compliance checking',
      'Bundle size analysis',
      'Visual regression testing'
    ];
    
    prePush: [
      'Cross-browser compatibility testing',
      'Performance budget validation',
      'SEO optimization checks',
      'Progressive web app compliance'
    ];
  };
  
  mobile: {
    preCommit: [
      'Platform-specific linting',
      'App store guideline compliance',
      'Device compatibility checking',
      'Battery usage optimization'
    ];
    
    prePush: [
      'Automated UI testing',
      'Performance profiling',
      'Memory leak detection',
      'App size optimization'
    ];
  };
  
  devops: {
    preCommit: [
      'Infrastructure as code validation',
      'Security policy compliance',
      'Configuration drift detection',
      'Cost optimization analysis'
    ];
    
    prePush: [
      'Infrastructure testing',
      'Deployment pipeline validation',
      'Monitoring and alerting setup',
      'Disaster recovery testing'
    ];
  };
}
```

### Shared Hook Configuration

```javascript
// shared-hooks/config.js
module.exports = {
  // Team-wide configuration
  team: {
    commitMessageFormat: /^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}/,
    maxFileSize: 50 * 1024 * 1024, // 50MB
    protectedBranches: ['main', 'develop'],
    requiredChecks: ['tests', 'linting', 'security']
  },
  
  // Discipline-specific configurations
  disciplines: {
    backend: {
      testCommand: 'npm run test:backend',
      lintCommand: 'npm run lint:backend',
      securityCommand: 'npm audit',
      filePatterns: ['src/api/**', 'src/services/**', 'src/models/**']
    },
    
    frontend: {
      testCommand: 'npm run test:frontend',
      lintCommand: 'npm run lint:frontend',
      buildCommand: 'npm run build:frontend',
      filePatterns: ['src/components/**', 'src/pages/**', 'src/styles/**']
    },
    
    mobile: {
      testCommand: 'npm run test:mobile',
      lintCommand: 'npm run lint:mobile',
      buildCommand: 'npm run build:mobile',
      filePatterns: ['mobile/**', 'src/mobile/**']
    },
    
    devops: {
      testCommand: 'terraform plan',
      lintCommand: 'terraform fmt -check',
      securityCommand: 'tfsec .',
      filePatterns: ['infrastructure/**', 'terraform/**', '.github/**']
    }
  }
};
```

## Hook Management and Distribution

### Centralized Hook Management

```bash
#!/bin/bash
# scripts/setup-hooks.sh

HOOKS_DIR=".githooks"
GIT_HOOKS_DIR=".git/hooks"

echo "Setting up Git hooks for the team..."

# Create hooks directory if it doesn't exist
mkdir -p "$HOOKS_DIR"

# Copy hooks to .git/hooks and make them executable
for hook in "$HOOKS_DIR"/*; do
    if [ -f "$hook" ]; then
        hook_name=$(basename "$hook")
        cp "$hook" "$GIT_HOOKS_DIR/$hook_name"
        chmod +x "$GIT_HOOKS_DIR/$hook_name"
        echo "Installed $hook_name hook"
    fi
done

# Set up git config to use hooks directory
git config core.hooksPath "$HOOKS_DIR"

echo "Git hooks setup complete!"
```

### Hook Testing Framework

```javascript
// test/hooks/pre-commit.test.js
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

describe('Pre-commit hook', () => {
  beforeEach(() => {
    // Setup test repository
    execSync('git init test-repo');
    process.chdir('test-repo');
    
    // Copy hook to test repository
    fs.copyFileSync('../.githooks/pre-commit', '.git/hooks/pre-commit');
    fs.chmodSync('.git/hooks/pre-commit', '755');
  });
  
  afterEach(() => {
    // Cleanup
    process.chdir('..');
    execSync('rm -rf test-repo');
  });
  
  test('should pass with valid JavaScript code', () => {
    // Create valid JS file
    fs.writeFileSync('test.js', 'const x = 1;\nconsole.log(x);\n');
    execSync('git add test.js');
    
    // Hook should pass
    expect(() => {
      execSync('git commit -m "feat: add test file"');
    }).not.toThrow();
  });
  
  test('should fail with linting errors', () => {
    // Create invalid JS file
    fs.writeFileSync('test.js', 'var x = 1\nconsole.log(x)');
    execSync('git add test.js');
    
    // Hook should fail
    expect(() => {
      execSync('git commit -m "feat: add test file"');
    }).toThrow();
  });
  
  test('should fail with invalid commit message', () => {
    fs.writeFileSync('test.js', 'const x = 1;\n');
    execSync('git add test.js');
    
    // Hook should fail with invalid message
    expect(() => {
      execSync('git commit -m "invalid message"');
    }).toThrow();
  });
});
```

### Hook Monitoring and Metrics

```typescript
interface HookMetrics {
  performance: {
    preCommitDuration: number;
    prePushDuration: number;
    testExecutionTime: number;
    lintingTime: number;
  };
  
  quality: {
    lintingErrorsBlocked: number;
    testFailuresBlocked: number;
    securityIssuesBlocked: number;
    commitMessageViolations: number;
  };
  
  adoption: {
    hooksEnabled: number;
    hooksDisabled: number;
    bypassFrequency: number;
    teamCompliance: number;
  };
  
  tracking: {
    logHookExecution: (hook: string, duration: number, success: boolean) => void;
    reportMetrics: () => HookMetrics;
    generateReport: () => string;
  };
}
```

---

*Git hooks provide automated quality gates and workflow enforcement for cross-functional teams. Implement hooks gradually, test thoroughly, and ensure they add value without creating excessive friction.*
