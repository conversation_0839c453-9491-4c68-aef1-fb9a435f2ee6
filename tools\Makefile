# Makefile for Senior Full-Stack Development Workspace
# This Makefile provides common development tasks and workflows

.PHONY: help install-tools setup-env clean test lint format docker-build docker-up docker-down

# Default target
help: ## Show this help message
	@echo "Senior Full-Stack Developer Workspace"
	@echo "====================================="
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development Environment Setup
install-tools: ## Install development tools and dependencies
	@echo "🔧 Installing development tools..."
	@if command -v node >/dev/null 2>&1; then \
		echo "✅ Node.js is installed"; \
	else \
		echo "❌ Node.js is not installed. Please install Node.js 18+"; \
		exit 1; \
	fi
	@if command -v python >/dev/null 2>&1; then \
		echo "✅ Python is installed"; \
	else \
		echo "❌ Python is not installed. Please install Python 3.11+"; \
		exit 1; \
	fi
	@if command -v docker >/dev/null 2>&1; then \
		echo "✅ Docker is installed"; \
	else \
		echo "❌ Docker is not installed. Please install Docker"; \
		exit 1; \
	fi
	@echo "🎉 All required tools are installed!"

setup-env: ## Set up development environment
	@echo "🚀 Setting up development environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env 2>/dev/null || echo "# Environment variables" > .env; \
		echo "📝 Created .env file"; \
	fi
	@echo "✅ Development environment ready!"

# Project Management
create-project: ## Create a new project (usage: make create-project NAME=my-project TYPE=react-ts)
	@if [ -z "$(NAME)" ]; then \
		echo "❌ Please specify project name: make create-project NAME=my-project"; \
		exit 1; \
	fi
	@echo "🏗️ Creating project: $(NAME)"
	@powershell -ExecutionPolicy Bypass -File "./scripts/setup-project.ps1" -ProjectName "$(NAME)" -Template "$(or $(TYPE),react-ts)" $(if $(DOCKER),-WithDocker) $(if $(TESTING),-WithTesting) $(if $(CI),-WithCI)

# Code Quality
lint: ## Run linting on all projects
	@echo "🔍 Running linting..."
	@find projects -name "package.json" -execdir npm run lint \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run flake8 . \; 2>/dev/null || true
	@echo "✅ Linting completed!"

format: ## Format code in all projects
	@echo "🎨 Formatting code..."
	@find projects -name "package.json" -execdir npm run format \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run black . \; 2>/dev/null || true
	@echo "✅ Code formatting completed!"

type-check: ## Run type checking
	@echo "🔍 Running type checking..."
	@find projects -name "tsconfig.json" -execdir npx tsc --noEmit \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run mypy . \; 2>/dev/null || true
	@echo "✅ Type checking completed!"

# Testing
test: ## Run tests for all projects
	@echo "🧪 Running tests..."
	@find projects -name "package.json" -execdir npm test \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run pytest \; 2>/dev/null || true
	@echo "✅ Tests completed!"

test-coverage: ## Run tests with coverage
	@echo "🧪 Running tests with coverage..."
	@find projects -name "package.json" -execdir npm run test:coverage \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run pytest --cov \; 2>/dev/null || true
	@echo "✅ Coverage tests completed!"

test-e2e: ## Run end-to-end tests
	@echo "🎭 Running E2E tests..."
	@find projects -name "playwright.config.ts" -execdir npx playwright test \; 2>/dev/null || true
	@echo "✅ E2E tests completed!"

# Docker Operations
docker-build: ## Build Docker images for all projects
	@echo "🐳 Building Docker images..."
	@find projects -name "Dockerfile" -execdir docker build -t $$(basename $$(pwd)) . \; 2>/dev/null || true
	@echo "✅ Docker images built!"

docker-up: ## Start all Docker services
	@echo "🐳 Starting Docker services..."
	@find projects -name "docker-compose.yml" -execdir docker-compose up -d \; 2>/dev/null || true
	@echo "✅ Docker services started!"

docker-down: ## Stop all Docker services
	@echo "🐳 Stopping Docker services..."
	@find projects -name "docker-compose.yml" -execdir docker-compose down \; 2>/dev/null || true
	@echo "✅ Docker services stopped!"

docker-logs: ## View Docker logs
	@echo "📋 Viewing Docker logs..."
	@find projects -name "docker-compose.yml" -execdir docker-compose logs -f \; 2>/dev/null || true

# Database Operations
db-migrate: ## Run database migrations
	@echo "🗄️ Running database migrations..."
	@find projects -name "alembic.ini" -execdir poetry run alembic upgrade head \; 2>/dev/null || true
	@find projects -name "knexfile.js" -execdir npx knex migrate:latest \; 2>/dev/null || true
	@echo "✅ Database migrations completed!"

db-seed: ## Seed database with test data
	@echo "🌱 Seeding database..."
	@find projects -name "seeds" -type d -execdir npm run db:seed \; 2>/dev/null || true
	@find projects -name "seeds.py" -execdir poetry run python seeds.py \; 2>/dev/null || true
	@echo "✅ Database seeding completed!"

db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "⚠️ This will delete all database data. Are you sure? [y/N]" && read ans && [ $${ans:-N} = y ]
	@echo "🗄️ Resetting database..."
	@find projects -name "alembic.ini" -execdir poetry run alembic downgrade base \; 2>/dev/null || true
	@find projects -name "alembic.ini" -execdir poetry run alembic upgrade head \; 2>/dev/null || true
	@make db-seed
	@echo "✅ Database reset completed!"

# Performance and Monitoring
perf-test: ## Run performance tests
	@echo "⚡ Running performance tests..."
	@find projects -name "artillery.yml" -execdir npx artillery run artillery.yml \; 2>/dev/null || true
	@find projects -name "locustfile.py" -execdir poetry run locust --headless -u 10 -r 2 -t 30s \; 2>/dev/null || true
	@echo "✅ Performance tests completed!"

lighthouse: ## Run Lighthouse audits
	@echo "🔍 Running Lighthouse audits..."
	@find projects -name "lighthouse.config.js" -execdir npx lighthouse-ci autorun \; 2>/dev/null || true
	@echo "✅ Lighthouse audits completed!"

# Security
security-scan: ## Run security scans
	@echo "🔒 Running security scans..."
	@find projects -name "package.json" -execdir npm audit \; 2>/dev/null || true
	@find projects -name "pyproject.toml" -execdir poetry run safety check \; 2>/dev/null || true
	@echo "✅ Security scans completed!"

# Cleanup
clean: ## Clean up build artifacts and dependencies
	@echo "🧹 Cleaning up..."
	@find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete 2>/dev/null || true
	@find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "✅ Cleanup completed!"

clean-docker: ## Clean up Docker resources
	@echo "🐳 Cleaning up Docker resources..."
	@docker system prune -f
	@docker volume prune -f
	@echo "✅ Docker cleanup completed!"

# Development Workflows
dev: ## Start development environment
	@echo "🚀 Starting development environment..."
	@make docker-up
	@make db-migrate
	@echo "✅ Development environment ready!"
	@echo "🌐 Access your applications:"
	@echo "   Frontend: http://localhost:3000"
	@echo "   Backend API: http://localhost:8000"
	@echo "   Database: localhost:5432"
	@echo "   Redis: localhost:6379"

stop: ## Stop development environment
	@echo "🛑 Stopping development environment..."
	@make docker-down
	@echo "✅ Development environment stopped!"

restart: ## Restart development environment
	@make stop
	@make dev

# CI/CD Simulation
ci: ## Simulate CI pipeline locally
	@echo "🔄 Running CI pipeline simulation..."
	@make lint
	@make type-check
	@make test-coverage
	@make security-scan
	@make docker-build
	@echo "✅ CI pipeline simulation completed!"

# Learning and Documentation
docs: ## Generate documentation
	@echo "📚 Generating documentation..."
	@find projects -name "typedoc.json" -execdir npx typedoc \; 2>/dev/null || true
	@find projects -name "docs" -type d -execdir poetry run sphinx-build -b html docs docs/_build \; 2>/dev/null || true
	@echo "✅ Documentation generated!"

learning-progress: ## Show learning progress
	@echo "📈 Learning Progress Report"
	@echo "=========================="
	@echo ""
	@echo "Projects completed:"
	@find projects -name "README.md" -exec grep -l "Status.*Completed" {} \; | wc -l | xargs echo "  ✅"
	@echo ""
	@echo "Projects in progress:"
	@find projects -name "README.md" -exec grep -l "Status.*In Progress" {} \; | wc -l | xargs echo "  🚧"
	@echo ""
	@echo "Total test coverage:"
	@find . -name "coverage-summary.json" -exec cat {} \; 2>/dev/null | grep -o '"lines":{"pct":[0-9.]*' | grep -o '[0-9.]*' | awk '{sum+=$1; count++} END {if(count>0) printf "  📊 %.1f%%\n", sum/count; else print "  📊 No coverage data"}'

# Advanced Operations
benchmark: ## Run benchmarks
	@echo "⚡ Running benchmarks..."
	@find projects -name "benchmark.js" -execdir node benchmark.js \; 2>/dev/null || true
	@find projects -name "benchmark.py" -execdir poetry run python benchmark.py \; 2>/dev/null || true
	@echo "✅ Benchmarks completed!"

profile: ## Profile application performance
	@echo "🔍 Profiling application performance..."
	@find projects -name "package.json" -execdir npm run profile \; 2>/dev/null || true
	@echo "✅ Profiling completed!"

# Help for specific areas
help-docker: ## Show Docker-specific help
	@echo "🐳 Docker Commands:"
	@echo "  make docker-build  - Build all Docker images"
	@echo "  make docker-up     - Start all services"
	@echo "  make docker-down   - Stop all services"
	@echo "  make docker-logs   - View service logs"
	@echo "  make clean-docker  - Clean up Docker resources"

help-testing: ## Show testing-specific help
	@echo "🧪 Testing Commands:"
	@echo "  make test          - Run all unit tests"
	@echo "  make test-coverage - Run tests with coverage"
	@echo "  make test-e2e      - Run end-to-end tests"
	@echo "  make perf-test     - Run performance tests"
	@echo "  make lighthouse    - Run Lighthouse audits"

help-db: ## Show database-specific help
	@echo "🗄️ Database Commands:"
	@echo "  make db-migrate    - Run database migrations"
	@echo "  make db-seed       - Seed database with test data"
	@echo "  make db-reset      - Reset database (WARNING: deletes data)"
