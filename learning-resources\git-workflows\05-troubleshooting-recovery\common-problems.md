# Common Git Problems and Solutions

This guide covers the most frequent Git issues encountered by cross-functional teams and provides step-by-step solutions for technical leaders and team members.

## Merge Conflicts

### Understanding Merge Conflicts

```typescript
interface MergeConflict {
  cause: 'Same lines modified in different branches';
  indicators: [
    'Git merge/rebase stops with conflict message',
    'Files contain conflict markers (<<<<<<< ======= >>>>>>>)',
    'Git status shows "both modified" files'
  ];
  
  conflictMarkers: {
    incoming: '<<<<<<< HEAD (or branch name)';
    separator: '=======';
    current: '>>>>>>> branch-name';
  };
  
  example: `
    <<<<<<< HEAD
    const apiUrl = 'https://api.production.com';
    =======
    const apiUrl = 'https://api.staging.com';
    >>>>>>> feature/api-update
  `;
}
```

### Resolving Merge Conflicts

```bash
# 1. Identify conflicted files
git status
# Look for "both modified" files

# 2. Open conflicted file and resolve manually
# Remove conflict markers and choose/combine changes
# Example resolution:
# const apiUrl = process.env.NODE_ENV === 'production' 
#   ? 'https://api.production.com' 
#   : 'https://api.staging.com';

# 3. Mark conflict as resolved
git add conflicted-file.js

# 4. Continue merge/rebase
git merge --continue
# or
git rebase --continue

# 5. If you want to abort the merge/rebase
git merge --abort
# or  
git rebase --abort
```

### Advanced Conflict Resolution

```bash
# Use merge tools for complex conflicts
git mergetool

# See conflict from both sides
git show :1:filename  # common ancestor
git show :2:filename  # current branch (HEAD)
git show :3:filename  # incoming branch

# Use specific merge strategy
git merge -X ours feature-branch    # Prefer current branch
git merge -X theirs feature-branch  # Prefer incoming branch

# Resolve conflicts in favor of one side
git checkout --ours filename       # Keep current branch version
git checkout --theirs filename     # Keep incoming branch version
git add filename
```

## Accidental Commits and History Issues

### Undoing the Last Commit

```typescript
interface UndoCommitScenarios {
  keepChanges: {
    command: 'git reset --soft HEAD~1';
    effect: 'Uncommit but keep changes staged';
    useCase: 'Wrong commit message or want to add more changes';
  };
  
  unstageChanges: {
    command: 'git reset HEAD~1';
    effect: 'Uncommit and unstage changes (default --mixed)';
    useCase: 'Want to recommit with different file grouping';
  };
  
  discardChanges: {
    command: 'git reset --hard HEAD~1';
    effect: 'Completely remove commit and changes';
    useCase: 'Commit was completely wrong';
    warning: 'Destructive - changes are lost permanently';
  };
}
```

### Fixing Commit Messages

```bash
# Fix the last commit message
git commit --amend -m "Corrected commit message"

# Fix commit message interactively
git commit --amend
# Opens editor to modify message

# Fix older commit messages (interactive rebase)
git rebase -i HEAD~3
# Change 'pick' to 'reword' for commits to modify

# Example interactive rebase:
# pick abc1234 Add user authentication
# reword def5678 Fix typo in validation  # This will be edited
# pick ghi9012 Update documentation
```

### Splitting Commits

```bash
# Split the last commit
git reset HEAD~1
git add file1.js
git commit -m "Add user validation"
git add file2.js  
git commit -m "Add password hashing"

# Split older commits (interactive rebase)
git rebase -i HEAD~3
# Change 'pick' to 'edit' for commit to split

# When rebase stops at the commit:
git reset HEAD~1
# Stage and commit files separately
git add file1.js
git commit -m "First part of changes"
git add file2.js
git commit -m "Second part of changes"
git rebase --continue
```

## Branch Management Issues

### Recovering Deleted Branches

```bash
# Find deleted branch in reflog
git reflog
# Look for branch creation/deletion entries

# Recreate branch from reflog
git checkout -b recovered-branch HEAD@{5}

# Find dangling commits
git fsck --lost-found
git show <commit-hash>

# Recover from known commit hash
git checkout -b recovered-branch <commit-hash>
```

### Cleaning Up Branch Mess

```bash
# See all branches (local and remote)
git branch -a

# Delete merged local branches
git branch --merged main | grep -v main | xargs git branch -d

# Delete remote tracking branches for deleted remotes
git remote prune origin

# Force delete unmerged branch (be careful!)
git branch -D problematic-branch

# Rename current branch
git branch -m new-branch-name

# Rename other branch
git branch -m old-name new-name
```

### Synchronization Issues

```bash
# Your branch is ahead/behind remote
git status
# "Your branch is ahead of 'origin/main' by 2 commits"

# Push local commits to remote
git push origin main

# Pull remote changes
git pull origin main

# Force push (dangerous - use with caution)
git push --force-with-lease origin main

# Reset local branch to match remote
git reset --hard origin/main
```

## File and Content Issues

### Recovering Deleted Files

```bash
# File deleted but not committed
git checkout HEAD -- deleted-file.js

# File deleted and committed
git log --oneline --follow -- deleted-file.js
git checkout <commit-before-deletion> -- deleted-file.js

# Restore file from specific commit
git show <commit-hash>:path/to/file.js > recovered-file.js
```

### Removing Sensitive Data

```bash
# Remove file from last commit
git rm --cached sensitive-file.txt
git commit --amend --no-edit

# Remove file from history (use BFG or git filter-branch)
# Install BFG Repo-Cleaner first
java -jar bfg.jar --delete-files sensitive-file.txt
git reflog expire --expire=now --all
git gc --prune=now --aggressive

# Alternative with git filter-branch (slower)
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch sensitive-file.txt' \
  --prune-empty --tag-name-filter cat -- --all
```

### Large File Issues

```bash
# Find large files in repository
git rev-list --objects --all | \
  git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
  awk '/^blob/ {print substr($0,6)}' | \
  sort --numeric-sort --key=2 | \
  tail -10

# Remove large files from history
java -jar bfg.jar --strip-blobs-bigger-than 50M

# Convert large files to Git LFS
git lfs track "*.zip"
git lfs track "*.pdf"
git add .gitattributes
git add large-file.zip
git commit -m "Convert large files to LFS"
```

## Remote Repository Issues

### Authentication Problems

```bash
# Update remote URL for HTTPS
git remote set-url origin https://github.com/username/repo.git

# Update remote URL for SSH
git remote set-<NAME_EMAIL>:username/repo.git

# Check current remote URLs
git remote -v

# Test SSH connection
ssh -T **************

# Cache credentials (HTTPS)
git config --global credential.helper cache
git config --global credential.helper 'cache --timeout=3600'
```

### Remote Synchronization Issues

```bash
# Remote branch doesn't exist locally
git fetch origin
git checkout -b local-branch origin/remote-branch

# Local branch not tracking remote
git branch --set-upstream-to=origin/main main

# Remote branch was force-pushed
git fetch origin
git reset --hard origin/main

# Multiple remotes confusion
git remote -v
git fetch --all
git branch -vv  # See tracking relationships
```

## Cross-Functional Team Specific Issues

### API Integration Conflicts

```typescript
interface APIConflictResolution {
  scenario: 'Backend API changes conflict with frontend expectations';
  
  detection: [
    'Integration tests failing',
    'Frontend build errors',
    'Mobile app crashes'
  ];
  
  resolution: [
    'Identify API contract changes',
    'Coordinate with affected teams',
    'Implement backward compatibility',
    'Update API documentation',
    'Plan coordinated deployment'
  ];
  
  prevention: [
    'Use API versioning',
    'Implement contract testing',
    'Maintain API documentation',
    'Coordinate breaking changes'
  ];
}
```

### Database Migration Conflicts

```bash
# Multiple developers created migrations
# Conflict: same migration number or conflicting schema changes

# 1. Identify conflicting migrations
ls migrations/
# Look for duplicate numbers or conflicting changes

# 2. Resolve migration conflicts
# Rename migrations to have sequential numbers
mv migrations/002_add_user_table.sql migrations/003_add_user_table.sql

# 3. Test migration sequence
npm run migrate:down  # Rollback
npm run migrate:up    # Apply in correct order

# 4. Update migration tracking
# Ensure migration tracking table is consistent
```

### Environment Configuration Conflicts

```bash
# Different environment configs causing issues

# 1. Identify configuration differences
git diff main feature-branch -- config/

# 2. Resolve environment-specific conflicts
# Keep environment-specific values separate
git checkout --ours config/production.yml
git checkout --theirs config/development.yml

# 3. Use environment variable templates
# Create .env.example with placeholder values
# Keep actual .env files out of version control
```

## Emergency Recovery Procedures

### Repository Corruption

```bash
# Check repository integrity
git fsck --full

# Repair minor corruption
git gc --aggressive

# Recover from backup
# If you have a recent clone:
cd ../backup-repo
git remote add corrupted /path/to/corrupted/repo
git fetch corrupted
git push origin --all
git push origin --tags

# Nuclear option: re-clone
git clone https://github.com/username/repo.git repo-recovered
cd repo-recovered
# Manually restore any local changes
```

### Accidental Force Push

```bash
# If you force-pushed and need to recover
# Check reflog on the remote (if accessible)
git reflog origin/main

# Restore from reflog
git reset --hard origin/main@{1}
git push --force-with-lease origin main

# If others pulled the bad changes
# Coordinate with team to reset their local branches
# Team members should run:
git fetch origin
git reset --hard origin/main
```

### Lost Work Recovery

```bash
# Check reflog for lost commits
git reflog
git log --walk-reflogs

# Check stash list
git stash list
git stash show -p stash@{0}

# Find dangling commits
git fsck --lost-found
ls .git/lost-found/commit/

# Examine found commits
git show <commit-hash>

# Create branch from recovered commit
git checkout -b recovered-work <commit-hash>
```

## Prevention Strategies

### Backup and Safety Measures

```typescript
interface GitSafetyMeasures {
  regularBackups: [
    'Clone repositories to multiple locations',
    'Use automated backup services',
    'Maintain local mirrors of critical repositories'
  ];
  
  branchProtection: [
    'Enable branch protection rules',
    'Require pull request reviews',
    'Prevent force pushes to main branches',
    'Require status checks to pass'
  ];
  
  teamPractices: [
    'Regular git training for team members',
    'Code review processes',
    'Clear branching strategies',
    'Documentation of Git workflows'
  ];
  
  tooling: [
    'Git hooks for validation',
    'Automated testing in CI/CD',
    'Git aliases for common operations',
    'Visual Git tools for complex operations'
  ];
}
```

### Monitoring and Alerts

```bash
# Set up repository monitoring
# Monitor for:
# - Large commits
# - Force pushes
# - Failed merges
# - Repository size growth

# Example monitoring script
#!/bin/bash
# monitor-repo.sh

REPO_SIZE=$(du -sh .git | cut -f1)
LARGE_FILES=$(git rev-list --objects --all | \
  git cat-file --batch-check='%(objectsize) %(rest)' | \
  awk '$1 > 10485760 {print $2}' | wc -l)

echo "Repository size: $REPO_SIZE"
echo "Large files (>10MB): $LARGE_FILES"

if [ "$LARGE_FILES" -gt 0 ]; then
    echo "Warning: Large files detected in repository"
fi
```

---

*Most Git problems can be resolved with the right knowledge and tools. The key is to stay calm, understand what happened, and apply the appropriate recovery technique. Always create backups before attempting complex recovery operations.*
