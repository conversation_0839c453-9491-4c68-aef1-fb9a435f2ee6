# Code Examples

## 💻 Working Code Samples for AI Applications

Complete, working code examples for integrating Portkey.ai into various types of AI applications, from simple chatbots to complex multi-model systems.

---

## 📁 Code Examples Structure

### **Language-Specific Examples**
- [`nodejs/`](./nodejs/) - Node.js/JavaScript implementations
- [`python/`](./python/) - Python implementations
- [`typescript/`](./typescript/) - TypeScript implementations
- [`rest-api/`](./rest-api/) - Direct REST API examples

### **Application Examples**
- [`chatbot/`](./chatbot/) - Conversational AI implementations
- [`content-generation/`](./content-generation/) - Text and content creation
- [`code-assistant/`](./code-assistant/) - Programming help and automation
- [`data-analysis/`](./data-analysis/) - Document processing and insights

### **Integration Patterns**
- [`routing-strategies/`](./routing-strategies/) - Model selection and fallback examples
- [`caching-examples/`](./caching-examples/) - Performance optimization patterns
- [`observability/`](./observability/) - Monitoring and analytics setup
- [`production-ready/`](./production-ready/) - Enterprise-grade implementations

---

## 🚀 Quick Start Examples

### **Basic Chat Completion (Node.js)**

```javascript
// basic-chat.js
import Portkey from 'portkey-ai';
import dotenv from 'dotenv';

dotenv.config();

const portkey = new Portkey({
  apiKey: process.env.PORTKEY_API_KEY,
  virtualKey: process.env.PORTKEY_VIRTUAL_KEY
});

async function basicChat() {
  try {
    const response = await portkey.chat.completions.create({
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Explain quantum computing in simple terms." }
      ],
      model: "gpt-3.5-turbo",
      max_tokens: 200,
      temperature: 0.7
    });

    console.log('AI Response:', response.choices[0].message.content);
    console.log('Usage:', response.usage);
    
    return response;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

// Run the example
basicChat();
```

### **Multi-Model Fallback (Python)**

```python
# multi_model_fallback.py
from portkey_ai import Portkey
import os
from dotenv import load_dotenv

load_dotenv()

portkey = Portkey(
    api_key=os.getenv("PORTKEY_API_KEY"),
    virtual_key=os.getenv("PORTKEY_VIRTUAL_KEY")
)

def multi_model_chat():
    # Configuration with fallback strategy
    config = {
        "strategy": {"mode": "fallback"},
        "targets": [
            {"provider": "openai", "model": "gpt-4"},
            {"provider": "anthropic", "model": "claude-2"},
            {"provider": "openai", "model": "gpt-3.5-turbo"}
        ],
        "retry": {
            "attempts": 3,
            "backoff": "exponential"
        }
    }
    
    try:
        response = portkey.chat.completions.create(
            config=config,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Write a haiku about artificial intelligence."}
            ],
            max_tokens=100
        )
        
        print("AI Response:", response.choices[0].message.content)
        print("Model Used:", response.model)
        print("Usage:", response.usage)
        
        return response
    except Exception as error:
        print("Error:", str(error))
        raise error

if __name__ == "__main__":
    multi_model_chat()
```

---

## 🤖 Chatbot Implementation

### **Advanced Chatbot with Context Management**

```javascript
// advanced-chatbot.js
import Portkey from 'portkey-ai';
import { v4 as uuidv4 } from 'uuid';

class AdvancedChatbot {
  constructor() {
    this.portkey = new Portkey({
      apiKey: process.env.PORTKEY_API_KEY,
      virtualKey: process.env.PORTKEY_VIRTUAL_KEY
    });
    
    this.conversations = new Map(); // In production, use a database
    
    this.config = {
      strategy: { mode: "fallback" },
      targets: [
        { provider: "openai", model: "gpt-4" },
        { provider: "anthropic", model: "claude-2" },
        { provider: "openai", model: "gpt-3.5-turbo" }
      ],
      cache: {
        mode: "semantic",
        ttl: 1800, // 30 minutes
        similarity_threshold: 0.92
      }
    };
  }

  async startConversation(userId, systemPrompt = null) {
    const conversationId = uuidv4();
    const conversation = {
      id: conversationId,
      userId: userId,
      messages: [],
      createdAt: new Date(),
      lastActivity: new Date()
    };

    if (systemPrompt) {
      conversation.messages.push({
        role: "system",
        content: systemPrompt
      });
    }

    this.conversations.set(conversationId, conversation);
    return conversationId;
  }

  async sendMessage(conversationId, userMessage) {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    // Add user message to conversation
    conversation.messages.push({
      role: "user",
      content: userMessage,
      timestamp: new Date()
    });

    // Manage context window (keep last 20 messages)
    const contextMessages = conversation.messages.slice(-20);

    try {
      const response = await this.portkey.chat.completions.create({
        config: this.config,
        messages: contextMessages,
        max_tokens: 500,
        temperature: 0.7,
        metadata: {
          conversation_id: conversationId,
          user_id: conversation.userId,
          message_count: conversation.messages.length
        }
      });

      const assistantMessage = {
        role: "assistant",
        content: response.choices[0].message.content,
        timestamp: new Date(),
        model: response.model,
        usage: response.usage
      };

      // Add assistant response to conversation
      conversation.messages.push(assistantMessage);
      conversation.lastActivity = new Date();

      return {
        message: assistantMessage.content,
        conversationId: conversationId,
        usage: response.usage,
        model: response.model
      };

    } catch (error) {
      console.error('Chat error:', error);
      
      // Fallback response for errors
      const fallbackMessage = "I'm experiencing technical difficulties. Please try again.";
      conversation.messages.push({
        role: "assistant",
        content: fallbackMessage,
        timestamp: new Date(),
        error: true
      });

      return {
        message: fallbackMessage,
        conversationId: conversationId,
        error: true
      };
    }
  }

  getConversationHistory(conversationId) {
    const conversation = this.conversations.get(conversationId);
    return conversation ? conversation.messages : null;
  }

  async summarizeConversation(conversationId) {
    const conversation = this.conversations.get(conversationId);
    if (!conversation || conversation.messages.length < 4) {
      return null;
    }

    const conversationText = conversation.messages
      .filter(msg => msg.role !== 'system')
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    try {
      const response = await this.portkey.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "Summarize the following conversation in 2-3 sentences."
          },
          {
            role: "user",
            content: conversationText
          }
        ],
        model: "gpt-3.5-turbo",
        max_tokens: 150
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error('Summarization error:', error);
      return null;
    }
  }
}

// Usage example
async function runChatbotExample() {
  const chatbot = new AdvancedChatbot();
  
  // Start a conversation
  const conversationId = await chatbot.startConversation(
    "user123",
    "You are a helpful AI assistant specializing in technology topics."
  );
  
  // Send messages
  const response1 = await chatbot.sendMessage(
    conversationId,
    "What is machine learning?"
  );
  console.log('Bot:', response1.message);
  
  const response2 = await chatbot.sendMessage(
    conversationId,
    "Can you give me a practical example?"
  );
  console.log('Bot:', response2.message);
  
  // Get conversation summary
  const summary = await chatbot.summarizeConversation(conversationId);
  console.log('Summary:', summary);
}

export { AdvancedChatbot };
```

---

## 📝 Content Generation System

### **Multi-Type Content Generator**

```python
# content_generator.py
from portkey_ai import Portkey
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional

class ContentGenerator:
    def __init__(self):
        self.portkey = Portkey(
            api_key=os.getenv("PORTKEY_API_KEY"),
            virtual_key=os.getenv("PORTKEY_VIRTUAL_KEY")
        )
        
        # Different configs for different content types
        self.configs = {
            "blog_post": {
                "strategy": {"mode": "quality_optimized"},
                "targets": [
                    {"provider": "anthropic", "model": "claude-2"},
                    {"provider": "openai", "model": "gpt-4"}
                ],
                "cache": {"mode": "semantic", "ttl": 7200}
            },
            
            "social_media": {
                "strategy": {"mode": "cost_optimized"},
                "targets": [
                    {"provider": "openai", "model": "gpt-3.5-turbo"}
                ],
                "cache": {"mode": "simple", "ttl": 3600}
            },
            
            "technical_docs": {
                "strategy": {"mode": "quality_optimized"},
                "targets": [
                    {"provider": "openai", "model": "gpt-4"}
                ],
                "cache": {"mode": "semantic", "ttl": 86400}
            },
            
            "creative_writing": {
                "strategy": {"mode": "creativity_optimized"},
                "targets": [
                    {"provider": "anthropic", "model": "claude-2"}
                ],
                "cache": {"mode": "semantic", "ttl": 3600}
            }
        }
        
        self.system_prompts = {
            "blog_post": "You are an expert content writer who creates engaging, informative blog posts with clear structure and compelling narratives.",
            "social_media": "You are a social media expert who creates engaging, concise content optimized for social platforms.",
            "technical_docs": "You are a technical writer who creates clear, accurate, and comprehensive documentation.",
            "creative_writing": "You are a creative writer who crafts imaginative, engaging stories and content."
        }

    async def generate_content(
        self, 
        content_type: str, 
        prompt: str, 
        options: Optional[Dict] = None
    ) -> Dict:
        """Generate content based on type and prompt."""
        
        if content_type not in self.configs:
            raise ValueError(f"Unsupported content type: {content_type}")
        
        options = options or {}
        config = self.configs[content_type]
        system_prompt = self.system_prompts[content_type]
        
        # Build messages
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        # Add examples if provided
        if "examples" in options:
            for example in options["examples"]:
                messages.insert(-1, {"role": "user", "content": example["input"]})
                messages.insert(-1, {"role": "assistant", "content": example["output"]})
        
        try:
            response = await self.portkey.chat.completions.create(
                config=config,
                messages=messages,
                max_tokens=options.get("max_tokens", 1000),
                temperature=options.get("temperature", 0.7),
                metadata={
                    "content_type": content_type,
                    "user_id": options.get("user_id"),
                    "feature": "content_generation"
                }
            )
            
            content = response.choices[0].message.content
            
            # Post-process content based on type
            processed_content = self._post_process_content(content, content_type, options)
            
            return {
                "success": True,
                "content": processed_content,
                "metadata": {
                    "content_type": content_type,
                    "model": response.model,
                    "usage": response.usage,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as error:
            return {
                "success": False,
                "error": str(error),
                "content": None
            }

    def _post_process_content(self, content: str, content_type: str, options: Dict) -> str:
        """Post-process content based on type and options."""
        
        if content_type == "social_media":
            # Add hashtags if requested
            if options.get("add_hashtags"):
                hashtags = self._generate_hashtags(content, options.get("hashtag_count", 3))
                content += f"\n\n{hashtags}"
        
        elif content_type == "blog_post":
            # Add meta description if requested
            if options.get("add_meta_description"):
                meta_desc = self._generate_meta_description(content)
                content = f"Meta Description: {meta_desc}\n\n{content}"
        
        return content

    async def generate_batch_content(
        self, 
        requests: List[Dict]
    ) -> List[Dict]:
        """Generate multiple pieces of content concurrently."""
        
        tasks = []
        for request in requests:
            task = self.generate_content(
                request["content_type"],
                request["prompt"],
                request.get("options", {})
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "request_index": i
                })
            else:
                processed_results.append(result)
        
        return processed_results

    async def optimize_content(self, content: str, optimization_type: str) -> Dict:
        """Optimize existing content for specific purposes."""
        
        optimization_prompts = {
            "seo": "Optimize the following content for SEO while maintaining readability and quality:",
            "readability": "Improve the readability of the following content while preserving its meaning:",
            "engagement": "Rewrite the following content to be more engaging and compelling:",
            "conciseness": "Make the following content more concise while retaining all key information:"
        }
        
        if optimization_type not in optimization_prompts:
            raise ValueError(f"Unsupported optimization type: {optimization_type}")
        
        prompt = f"{optimization_prompts[optimization_type]}\n\n{content}"
        
        try:
            response = await self.portkey.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are an expert content optimizer."},
                    {"role": "user", "content": prompt}
                ],
                model="gpt-4",
                max_tokens=1500,
                temperature=0.3
            )
            
            return {
                "success": True,
                "optimized_content": response.choices[0].message.content,
                "optimization_type": optimization_type,
                "usage": response.usage
            }
            
        except Exception as error:
            return {
                "success": False,
                "error": str(error)
            }

# Usage example
async def run_content_generation_example():
    generator = ContentGenerator()
    
    # Generate a blog post
    blog_result = await generator.generate_content(
        "blog_post",
        "Write a comprehensive guide about sustainable living practices",
        {
            "max_tokens": 1500,
            "temperature": 0.7,
            "user_id": "user123"
        }
    )
    
    if blog_result["success"]:
        print("Blog Post Generated:")
        print(blog_result["content"])
        print(f"Model: {blog_result['metadata']['model']}")
        print(f"Tokens: {blog_result['metadata']['usage']['total_tokens']}")
    
    # Generate social media posts
    social_requests = [
        {
            "content_type": "social_media",
            "prompt": "Create a Twitter post about the benefits of renewable energy",
            "options": {"add_hashtags": True, "hashtag_count": 5}
        },
        {
            "content_type": "social_media", 
            "prompt": "Write a LinkedIn post about career growth in tech",
            "options": {"add_hashtags": True, "hashtag_count": 3}
        }
    ]
    
    social_results = await generator.generate_batch_content(social_requests)
    
    for i, result in enumerate(social_results):
        if result["success"]:
            print(f"\nSocial Media Post {i+1}:")
            print(result["content"])

if __name__ == "__main__":
    asyncio.run(run_content_generation_example())
```

---

## 🔧 Code Assistant Implementation

### **Intelligent Programming Helper**

```typescript
// code-assistant.ts
import Portkey from 'portkey-ai';

interface CodeRequest {
  language: string;
  task: 'generate' | 'review' | 'debug' | 'explain' | 'optimize';
  code?: string;
  description: string;
  context?: string;
}

interface CodeResponse {
  success: boolean;
  result?: string;
  explanation?: string;
  suggestions?: string[];
  error?: string;
  metadata?: {
    model: string;
    usage: any;
    confidence: number;
  };
}

class CodeAssistant {
  private portkey: Portkey;
  private configs: Record<string, any>;

  constructor() {
    this.portkey = new Portkey({
      apiKey: process.env.PORTKEY_API_KEY!,
      virtualKey: process.env.PORTKEY_VIRTUAL_KEY!
    });

    this.configs = {
      code_generation: {
        strategy: { mode: "quality_optimized" },
        targets: [
          { provider: "openai", model: "gpt-4", temperature: 0.1 },
          { provider: "anthropic", model: "claude-2", temperature: 0.1 }
        ],
        cache: { mode: "semantic", ttl: 3600 }
      },

      code_review: {
        strategy: { mode: "quality_optimized" },
        targets: [
          { provider: "anthropic", model: "claude-2", temperature: 0.2 },
          { provider: "openai", model: "gpt-4", temperature: 0.2 }
        ],
        cache: { mode: "simple", ttl: 1800 }
      },

      debugging: {
        strategy: { mode: "accuracy_optimized" },
        targets: [
          { provider: "openai", model: "gpt-4", temperature: 0.0 }
        ],
        cache: { mode: "semantic", ttl: 1800 }
      }
    };
  }

  async processCodeRequest(request: CodeRequest): Promise<CodeResponse> {
    try {
      const systemPrompt = this.getSystemPrompt(request.task, request.language);
      const userPrompt = this.buildUserPrompt(request);
      const config = this.getConfig(request.task);

      const response = await this.portkey.chat.completions.create({
        config: config,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        max_tokens: 2000,
        metadata: {
          task: request.task,
          language: request.language,
          feature: "code_assistant"
        }
      });

      const result = response.choices[0].message.content;
      const parsedResult = this.parseCodeResponse(result, request.task);

      return {
        success: true,
        result: parsedResult.code,
        explanation: parsedResult.explanation,
        suggestions: parsedResult.suggestions,
        metadata: {
          model: response.model,
          usage: response.usage,
          confidence: this.calculateConfidence(response)
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private getSystemPrompt(task: string, language: string): string {
    const prompts = {
      generate: `You are an expert ${language} programmer. Generate clean, efficient, and well-documented code based on the user's requirements. Include comments explaining key parts of the code.`,
      
      review: `You are a senior code reviewer. Analyze the provided ${language} code for:
- Code quality and best practices
- Potential bugs or issues
- Performance optimizations
- Security vulnerabilities
- Maintainability improvements
Provide specific, actionable feedback.`,
      
      debug: `You are a debugging expert for ${language}. Analyze the provided code and error information to:
- Identify the root cause of the issue
- Provide a clear explanation of what's wrong
- Suggest specific fixes
- Explain how to prevent similar issues`,
      
      explain: `You are a programming teacher. Explain the provided ${language} code in clear, educational terms:
- What the code does overall
- How each part works
- Key concepts and patterns used
- Why certain approaches were chosen`,
      
      optimize: `You are a performance optimization expert for ${language}. Analyze the code and:
- Identify performance bottlenecks
- Suggest optimizations
- Provide improved code examples
- Explain the performance impact of changes`
    };

    return prompts[task as keyof typeof prompts] || prompts.generate;
  }

  private buildUserPrompt(request: CodeRequest): string {
    let prompt = `Task: ${request.task}\nLanguage: ${request.language}\n\n`;
    
    if (request.description) {
      prompt += `Description: ${request.description}\n\n`;
    }
    
    if (request.code) {
      prompt += `Code:\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\n`;
    }
    
    if (request.context) {
      prompt += `Additional Context: ${request.context}\n\n`;
    }

    // Add task-specific instructions
    switch (request.task) {
      case 'generate':
        prompt += "Please provide complete, working code with comments.";
        break;
      case 'review':
        prompt += "Please provide a detailed code review with specific suggestions.";
        break;
      case 'debug':
        prompt += "Please identify the issue and provide a fix with explanation.";
        break;
      case 'explain':
        prompt += "Please provide a clear, educational explanation of the code.";
        break;
      case 'optimize':
        prompt += "Please suggest optimizations and provide improved code.";
        break;
    }

    return prompt;
  }

  private getConfig(task: string): any {
    const configMap = {
      generate: this.configs.code_generation,
      review: this.configs.code_review,
      debug: this.configs.debugging,
      explain: this.configs.code_review,
      optimize: this.configs.code_generation
    };

    return configMap[task as keyof typeof configMap] || this.configs.code_generation;
  }

  private parseCodeResponse(response: string, task: string): {
    code?: string;
    explanation?: string;
    suggestions?: string[];
  } {
    const result: any = {};

    // Extract code blocks
    const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
    const codeMatches = [...response.matchAll(codeBlockRegex)];
    
    if (codeMatches.length > 0) {
      result.code = codeMatches[0][1];
    }

    // Extract explanations and suggestions based on task
    if (task === 'review') {
      const suggestions = this.extractSuggestions(response);
      result.suggestions = suggestions;
    }

    // Everything else as explanation
    result.explanation = response.replace(codeBlockRegex, '').trim();

    return result;
  }

  private extractSuggestions(text: string): string[] {
    const suggestions: string[] = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
      if (line.match(/^[-*]\s+/) || line.match(/^\d+\.\s+/)) {
        suggestions.push(line.replace(/^[-*\d.]\s+/, '').trim());
      }
    }

    return suggestions;
  }

  private calculateConfidence(response: any): number {
    // Simple confidence calculation based on response characteristics
    const content = response.choices[0].message.content;
    const hasCodeBlocks = content.includes('```');
    const hasExplanation = content.length > 100;
    const hasStructure = content.includes('\n');

    let confidence = 0.5;
    if (hasCodeBlocks) confidence += 0.2;
    if (hasExplanation) confidence += 0.2;
    if (hasStructure) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  // Specialized methods for different programming tasks
  async generateFunction(
    language: string,
    functionName: string,
    description: string,
    parameters?: string[],
    returnType?: string
  ): Promise<CodeResponse> {
    const request: CodeRequest = {
      language,
      task: 'generate',
      description: `Create a function named "${functionName}" that ${description}`,
      context: parameters ? `Parameters: ${parameters.join(', ')}` : undefined
    };

    if (returnType) {
      request.context += `\nReturn type: ${returnType}`;
    }

    return this.processCodeRequest(request);
  }

  async reviewPullRequest(
    language: string,
    code: string,
    description: string
  ): Promise<CodeResponse> {
    return this.processCodeRequest({
      language,
      task: 'review',
      code,
      description: `Pull request: ${description}`,
      context: "Focus on code quality, security, and maintainability"
    });
  }

  async debugError(
    language: string,
    code: string,
    errorMessage: string,
    stackTrace?: string
  ): Promise<CodeResponse> {
    let context = `Error message: ${errorMessage}`;
    if (stackTrace) {
      context += `\nStack trace: ${stackTrace}`;
    }

    return this.processCodeRequest({
      language,
      task: 'debug',
      code,
      description: "Debug this error and provide a fix",
      context
    });
  }
}

// Usage example
async function runCodeAssistantExample() {
  const assistant = new CodeAssistant();

  // Generate a function
  const generateResult = await assistant.generateFunction(
    'typescript',
    'calculateFibonacci',
    'calculates the nth Fibonacci number efficiently',
    ['n: number'],
    'number'
  );

  if (generateResult.success) {
    console.log('Generated Function:');
    console.log(generateResult.result);
    console.log('\nExplanation:');
    console.log(generateResult.explanation);
  }

  // Review code
  const codeToReview = `
function quickSort(arr) {
  if (arr.length <= 1) return arr;
  const pivot = arr[0];
  const left = arr.slice(1).filter(x => x < pivot);
  const right = arr.slice(1).filter(x => x >= pivot);
  return [...quickSort(left), pivot, ...quickSort(right)];
}
  `;

  const reviewResult = await assistant.reviewPullRequest(
    'javascript',
    codeToReview,
    'Implement quicksort algorithm'
  );

  if (reviewResult.success) {
    console.log('\nCode Review:');
    console.log(reviewResult.explanation);
    if (reviewResult.suggestions) {
      console.log('\nSuggestions:');
      reviewResult.suggestions.forEach((suggestion, i) => {
        console.log(`${i + 1}. ${suggestion}`);
      });
    }
  }
}

export { CodeAssistant };
```

---

**These code examples provide practical, production-ready implementations for various AI use cases. Ready to explore best practices and optimization strategies?**

**Next**: [Best Practices →](../06-best-practices/README.md)
