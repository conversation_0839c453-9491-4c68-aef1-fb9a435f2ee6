# Team Git Standards and Guidelines

This document establishes Git standards and best practices for our cross-functional development team.

## Branch Naming Conventions

### Standard Format
```
type/scope/description
```

### Branch Types
- **feature/** - New functionality or enhancements
- **bugfix/** - Bug fixes for existing functionality  
- **hotfix/** - Critical production fixes
- **refactor/** - Code refactoring without functionality changes
- **docs/** - Documentation updates
- **test/** - Test additions or modifications
- **chore/** - Maintenance tasks and tooling

### Scope Guidelines
- **backend/** - Backend/API related changes
- **frontend/** - Frontend/UI related changes
- **mobile/** - Mobile app related changes
- **devops/** - Infrastructure and deployment changes
- **shared/** - Changes affecting multiple areas

### Examples
```bash
feature/backend/user-authentication
feature/frontend/dashboard-redesign
feature/mobile/push-notifications
bugfix/payment-validation-error
hotfix/security-vulnerability-fix
refactor/database-connection-pooling
docs/api-documentation-update
```

## Commit Message Standards

### Format
```
type(scope): description

[optional body]

[optional footer]
```

### Commit Types
- **feat** - New feature
- **fix** - Bug fix
- **docs** - Documentation changes
- **style** - Code style changes (formatting, semicolons, etc.)
- **refactor** - Code refactoring
- **test** - Adding or updating tests
- **chore** - Maintenance tasks

### Examples
```bash
feat(auth): add OAuth2 integration
fix(payment): handle invalid card numbers gracefully
docs(readme): update installation instructions
style(frontend): fix ESLint warnings
refactor(api): extract validation middleware
test(user): add integration tests for user service
chore(deps): update dependencies to latest versions
```

### Commit Message Rules
1. **Subject line**: 50 characters or less
2. **Body**: Wrap at 72 characters
3. **Use imperative mood**: "Add feature" not "Added feature"
4. **Explain what and why**: Not just what changed, but why
5. **Reference issues**: Include issue numbers when applicable

## Workflow Standards

### Feature Development Workflow

```bash
# 1. Start from updated main branch
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/backend/user-profile-api

# 3. Develop with frequent commits
git add src/api/user-profile.js
git commit -m "feat(api): add user profile endpoints"

git add tests/user-profile.test.js
git commit -m "test(api): add user profile endpoint tests"

# 4. Keep branch updated
git fetch origin
git rebase origin/main

# 5. Push and create pull request
git push origin feature/backend/user-profile-api
# Create PR via GitHub/GitLab interface

# 6. After merge, clean up
git checkout main
git pull origin main
git branch -d feature/backend/user-profile-api
```

### Code Review Requirements

#### Pull Request Checklist
- [ ] **Clear description** of changes and motivation
- [ ] **Tests added/updated** for new functionality
- [ ] **Documentation updated** if needed
- [ ] **No merge conflicts** with target branch
- [ ] **CI/CD pipeline passing** all checks
- [ ] **Code follows team style guidelines**
- [ ] **Breaking changes documented** and communicated

#### Review Requirements
- **Minimum 2 approvals** required
- **At least 1 approval** from discipline expert
- **Technical lead approval** for architectural changes
- **All automated checks** must pass
- **No unresolved conversations** in review

#### Review Guidelines
- **Be constructive** and specific in feedback
- **Explain reasoning** behind suggestions
- **Ask questions** to understand context
- **Approve when ready** - don't delay unnecessarily
- **Test locally** for complex changes

## Branch Protection Rules

### Main Branch Protection
- **Require pull request reviews** before merging
- **Dismiss stale reviews** when new commits are pushed
- **Require status checks** to pass before merging
- **Require branches to be up to date** before merging
- **Restrict pushes** that create merge commits
- **Restrict force pushes**

### Required Status Checks
- **Unit tests** must pass
- **Integration tests** must pass
- **Linting** must pass without errors
- **Security scan** must pass
- **Build** must succeed

## Merge Strategies

### Preferred Merge Strategy: Squash and Merge
- **Benefits**: Clean, linear history
- **Use for**: Feature branches with multiple commits
- **Result**: Single commit per feature in main branch

### When to Use Merge Commit
- **Large features** with significant sub-components
- **Collaborative branches** with multiple contributors
- **When preserving** detailed commit history is important

### When to Use Rebase and Merge
- **Small, clean commits** that add value individually
- **Well-structured** commit history
- **When linear history** is preferred

## Discipline-Specific Guidelines

### Backend Development
```bash
# API changes require coordination
feature/backend/api-v2-user-endpoints

# Database migrations in separate commits
git add migrations/001_add_user_profile_table.sql
git commit -m "feat(db): add user profile table migration"

git add src/models/user-profile.js
git commit -m "feat(api): add user profile model"

# Include API documentation updates
git add docs/api/user-profile.md
git commit -m "docs(api): add user profile endpoint documentation"
```

### Frontend Development
```bash
# Component development
feature/frontend/user-profile-components

# Separate commits for different concerns
git add src/components/UserProfile.jsx
git commit -m "feat(ui): add user profile component"

git add src/components/UserProfile.test.jsx
git commit -m "test(ui): add user profile component tests"

git add src/styles/UserProfile.css
git commit -m "style(ui): add user profile component styles"
```

### Mobile Development
```bash
# Platform-specific branches when needed
feature/mobile/ios-biometric-auth
feature/mobile/android-biometric-auth

# Shared mobile code
feature/mobile/biometric-auth-shared

# Include platform-specific considerations
git commit -m "feat(mobile): add biometric auth for iOS

- Implement Touch ID and Face ID support
- Handle biometric availability checking
- Add fallback to passcode authentication
- Update iOS deployment target to 12.0"
```

### DevOps/Infrastructure
```bash
# Infrastructure changes
feature/devops/kubernetes-deployment

# Separate infrastructure and application changes
git add kubernetes/deployment.yaml
git commit -m "feat(infra): add Kubernetes deployment configuration"

git add .github/workflows/deploy.yml
git commit -m "feat(ci): add automated deployment workflow"

# Include rollback procedures
git add docs/deployment-rollback.md
git commit -m "docs(infra): add deployment rollback procedures"
```

## Git Configuration

### Required Git Configuration
```bash
# Set up user information
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Set up default branch name
git config --global init.defaultBranch main

# Set up pull strategy
git config --global pull.rebase false

# Set up push strategy
git config --global push.default simple

# Enable automatic cleanup
git config --global fetch.prune true
```

### Recommended Git Aliases
```bash
# Add useful aliases
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
```

## Security Guidelines

### Sensitive Data Protection
- **Never commit** passwords, API keys, or secrets
- **Use environment variables** for configuration
- **Add .env files** to .gitignore
- **Use Git hooks** to scan for sensitive data
- **Rotate credentials** if accidentally committed

### Access Control
- **Use SSH keys** for Git authentication
- **Enable two-factor authentication** on Git hosting
- **Regularly review** repository access permissions
- **Use deploy keys** for automated systems
- **Audit access logs** regularly

## Troubleshooting Quick Reference

### Common Commands
```bash
# Undo last commit (keep changes)
git reset --soft HEAD~1

# Undo last commit (discard changes)
git reset --hard HEAD~1

# Fix commit message
git commit --amend -m "New message"

# Resolve merge conflicts
git status  # See conflicted files
# Edit files to resolve conflicts
git add resolved-file.js
git commit

# Abort merge/rebase
git merge --abort
git rebase --abort

# Clean up branches
git branch --merged main | grep -v main | xargs git branch -d
git remote prune origin
```

### Emergency Contacts
- **Technical Lead**: [Name] - [Contact]
- **DevOps Lead**: [Name] - [Contact]
- **Git Repository Admin**: [Name] - [Contact]

## Training Resources

### Required Reading
- [ ] Git Fundamentals (internal documentation)
- [ ] Team Workflow Guide
- [ ] Code Review Best Practices
- [ ] Security Guidelines

### Recommended Tools
- **Git GUI**: GitKraken, SourceTree, or VS Code Git integration
- **Merge Tools**: VS Code, IntelliJ IDEA, or dedicated merge tools
- **Command Line**: Git Bash (Windows) or Terminal (Mac/Linux)

### Training Schedule
- **New team members**: Git fundamentals training in first week
- **Quarterly reviews**: Team Git practices and improvements
- **As needed**: Advanced Git workshops for complex scenarios

---

*These standards ensure consistent, high-quality Git practices across our cross-functional team. All team members are expected to follow these guidelines and help maintain our collaborative development environment.*
