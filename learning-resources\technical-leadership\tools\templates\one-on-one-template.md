# One-on-One Meeting Template

A structured template for conducting effective one-on-one meetings with cross-functional team members, designed for part-time technical leaders.

## Meeting Preparation

### Pre-Meeting Checklist (5 minutes)

```typescript
interface OneOnOnePrep {
  reviewPrevious: {
    actionItems: 'Check status of previous action items';
    goals: 'Review progress on individual goals';
    feedback: 'Note any feedback given or received';
    concerns: 'Recall any ongoing issues or challenges';
  };
  
  currentContext: {
    recentWork: 'Review team member\'s recent contributions';
    projectStatus: 'Understand current project involvement';
    teamDynamics: 'Note any team interaction observations';
    stakeholderFeedback: 'Gather any relevant feedback from others';
  };
  
  agenda: {
    theirTopics: 'Ask team member to prepare topics in advance';
    yourTopics: 'Prepare your own discussion points';
    development: 'Plan career/skill development discussions';
    feedback: 'Prepare any feedback to share';
  };
}
```

### Team Member Pre-Work

Send this template to team members 24 hours before the meeting:

```markdown
## One-on-One Preparation

Please take a few minutes to think about these topics before our meeting:

### Your Agenda Items
- What topics would you like to discuss?
- Any challenges or blockers you're facing?
- Questions about projects, processes, or team dynamics?

### Recent Work Reflection
- What work are you most proud of recently?
- What has been most challenging?
- What would you like to do more/less of?

### Growth and Development
- What skills would you like to develop?
- Any learning opportunities you're interested in?
- Career goals or aspirations to discuss?

### Team and Process Feedback
- How are team processes working for you?
- Any suggestions for team improvements?
- Feedback on communication or collaboration?
```

## Meeting Structure (30 minutes)

### Opening (5 minutes)

```typescript
interface MeetingOpening {
  checkIn: {
    personal: 'How are you doing overall?';
    workload: 'How is your current workload feeling?';
    energy: 'What\'s your energy level like lately?';
    schedule: 'How is the part-time/full-time schedule working?';
  };
  
  agenda: {
    theirPriorities: 'What\'s most important for you to discuss today?';
    timeAllocation: 'How should we split our time?';
    urgentItems: 'Anything urgent we should address first?';
  };
}
```

### Their Agenda (15 minutes)

```typescript
interface TeamMemberAgenda {
  activeListening: {
    approach: 'Let them lead the conversation';
    techniques: [
      'Ask open-ended questions',
      'Paraphrase to confirm understanding',
      'Avoid jumping to solutions immediately',
      'Take notes on key points and concerns'
    ];
  };
  
  commonTopics: {
    workChallenges: {
      questions: [
        'What\'s been most challenging lately?',
        'What support would be most helpful?',
        'Are there any blockers I can help remove?'
      ];
      approach: 'Focus on how you can help, not just advice';
    };
    
    careerDevelopment: {
      questions: [
        'What skills do you want to develop?',
        'What kind of work energizes you most?',
        'Where do you see yourself growing?'
      ];
      approach: 'Connect to opportunities within current projects';
    };
    
    teamDynamics: {
      questions: [
        'How are relationships with other team members?',
        'Any communication challenges?',
        'What\'s working well in team collaboration?'
      ];
      approach: 'Look for patterns and improvement opportunities';
    };
    
    processImprovement: {
      questions: [
        'What processes are helping/hindering your work?',
        'Any suggestions for team improvements?',
        'What tools or resources would be helpful?'
      ];
      approach: 'Gather concrete suggestions for team optimization';
    };
  };
}
```

### Your Agenda (8 minutes)

```typescript
interface LeaderAgenda {
  feedback: {
    positive: {
      approach: 'Be specific about what they did well';
      format: 'When you [specific behavior], it [positive impact]';
      example: 'When you took initiative on the API documentation, it really helped the frontend team understand the integration';
    };
    
    developmental: {
      approach: 'Focus on growth opportunities, not criticism';
      format: 'I noticed [observation]. What are your thoughts on [improvement area]?';
      example: 'I noticed the code review feedback was quite detailed. What are your thoughts on balancing thoroughness with review speed?';
    };
  };
  
  expectations: {
    clarification: 'Ensure alignment on role expectations and priorities';
    adjustments: 'Discuss any changes in responsibilities or focus';
    support: 'Confirm what support they need to meet expectations';
  };
  
  organizationalUpdates: {
    context: 'Share relevant business or team context';
    changes: 'Communicate any upcoming changes that affect them';
    opportunities: 'Highlight growth or learning opportunities';
  };
}
```

### Wrap-up and Action Items (2 minutes)

```typescript
interface MeetingWrapUp {
  actionItems: {
    theirActions: 'What will they commit to doing?';
    yourActions: 'What will you commit to doing?';
    timeline: 'When will these be completed?';
    followUp: 'How will you check on progress?';
  };
  
  nextMeeting: {
    timing: 'Confirm next meeting time';
    topics: 'Any specific topics to prepare for?';
    format: 'Any adjustments to meeting format needed?';
  };
  
  appreciation: {
    recognition: 'Acknowledge their contributions';
    gratitude: 'Thank them for their openness and input';
    encouragement: 'Provide encouragement for upcoming challenges';
  };
}
```

## Discipline-Specific Conversation Guides

### Backend Developers

```typescript
interface BackendOneOnOne {
  technicalTopics: {
    architecture: 'How do you feel about our current system architecture?';
    performance: 'Any performance concerns or optimization opportunities?';
    codeQuality: 'How can we improve our code review and quality processes?';
    tooling: 'What development tools or processes would improve your productivity?';
  };
  
  collaborationTopics: {
    apiDesign: 'How is the API design process working with frontend/mobile?';
    requirements: 'Are you getting clear requirements from product/stakeholders?';
    documentation: 'How can we improve technical documentation?';
    knowledge: 'Any knowledge gaps in the team we should address?';
  };
  
  growthTopics: {
    skills: 'What backend technologies or patterns interest you?';
    leadership: 'Any interest in mentoring or technical leadership?';
    architecture: 'Would you like more involvement in system design decisions?';
    crossFunctional: 'Interest in learning more about frontend/mobile/DevOps?';
  };
}
```

### Frontend Developers

```typescript
interface FrontendOneOnOne {
  technicalTopics: {
    userExperience: 'How do you feel about the user experience we\'re delivering?';
    performance: 'Any frontend performance concerns or opportunities?';
    tooling: 'How are our build tools and development workflow?';
    testing: 'How can we improve our frontend testing strategy?';
  };
  
  collaborationTopics: {
    design: 'How is collaboration with design/product working?';
    backend: 'Any challenges with API integration or backend collaboration?';
    mobile: 'How can we better coordinate with mobile development?';
    feedback: 'Are you getting enough user feedback on your work?';
  };
  
  growthTopics: {
    skills: 'What frontend technologies or frameworks interest you?';
    design: 'Any interest in developing design or UX skills?';
    fullStack: 'Interest in learning more backend technologies?';
    leadership: 'Would you like to take on more technical leadership?';
  };
}
```

### Mobile Developers

```typescript
interface MobileOneOnOne {
  technicalTopics: {
    platforms: 'How are you balancing iOS/Android development?';
    performance: 'Any mobile-specific performance challenges?';
    userExperience: 'How do you feel about the mobile user experience?';
    tooling: 'Are our mobile development tools and processes effective?';
  };
  
  collaborationTopics: {
    apiIntegration: 'How well are backend APIs working for mobile needs?';
    designConsistency: 'How can we improve consistency between web and mobile?';
    testing: 'How can we improve mobile testing and QA processes?';
    deployment: 'Any challenges with app store deployment processes?';
  };
  
  growthTopics: {
    platforms: 'Interest in expanding to other mobile platforms?';
    crossPlatform: 'Any interest in React Native or Flutter?';
    backend: 'Would you like to learn more about backend development?';
    leadership: 'Interest in leading mobile development initiatives?';
  };
}
```

### DevOps Engineers

```typescript
interface DevOpsOneOnOne {
  technicalTopics: {
    infrastructure: 'How is our current infrastructure meeting team needs?';
    automation: 'What manual processes should we prioritize for automation?';
    monitoring: 'How can we improve our monitoring and alerting?';
    security: 'Any security concerns or improvement opportunities?';
  };
  
  collaborationTopics: {
    developerExperience: 'How can we improve the developer experience?';
    deployment: 'How are deployment processes working for the team?';
    support: 'What kind of support do developers need most?';
    communication: 'How can we better communicate infrastructure changes?';
  };
  
  growthTopics: {
    cloudTechnologies: 'What cloud technologies interest you?';
    automation: 'Areas where you\'d like to expand automation skills?';
    security: 'Interest in developing security expertise?';
    leadership: 'Would you like to lead infrastructure initiatives?';
  };
}
```

## Follow-up and Documentation

### Meeting Notes Template

```markdown
# One-on-One Notes: [Name] - [Date]

## Key Discussion Points
- [Topic 1 with brief summary]
- [Topic 2 with brief summary]
- [Topic 3 with brief summary]

## Action Items
### [Name]'s Actions
- [ ] [Action item] - Due: [Date]
- [ ] [Action item] - Due: [Date]

### My Actions
- [ ] [Action item] - Due: [Date]
- [ ] [Action item] - Due: [Date]

## Growth and Development
- Current focus: [Development area]
- Next steps: [Specific actions]
- Support needed: [How you can help]

## Team/Process Feedback
- What's working: [Positive feedback]
- Improvement opportunities: [Suggestions]
- Action needed: [Process changes to consider]

## Next Meeting
- Date: [Next meeting date]
- Topics to prepare: [Specific items]
- Follow-up needed: [Items to check on]
```

### Tracking and Trends

```typescript
interface OneOnOneTracking {
  frequency: 'Track patterns across multiple meetings';
  
  patterns: {
    engagement: 'Is engagement increasing or decreasing?';
    challenges: 'Are similar challenges recurring?';
    growth: 'Is development progressing as planned?';
    satisfaction: 'How is overall job satisfaction trending?';
  };
  
  actionItemTracking: {
    completion: 'Track completion rate of action items';
    barriers: 'Identify common barriers to completion';
    support: 'Adjust support based on completion patterns';
  };
  
  teamInsights: {
    commonThemes: 'Identify themes across team members';
    processImprovements: 'Gather suggestions for team-wide improvements';
    culturalHealth: 'Monitor team culture and dynamics';
  };
}
```

---

*Effective one-on-ones are the foundation of strong leadership relationships. Focus on listening, supporting growth, and building trust through consistent, valuable conversations.*
